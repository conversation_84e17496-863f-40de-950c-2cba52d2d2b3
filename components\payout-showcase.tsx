"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DollarSign, TrendingUp, Award, Star, Users, Zap } from "lucide-react"
import Image from "next/image"
import { useState, useEffect } from "react"

export default function PayoutShowcase() {
  const [currentIndex, setCurrentIndex] = useState(0)

  const payoutCertificates = [
    {
      id: 1,
      trader: "ARWIN SAXENA",
      amount: "$2,541.54",
      date: "06 APR 2025",
      country: "IN",
      category: "Forex",
      zIndex: 30,
      rotation: 0,
      translateX: 0,
    },
    {
      id: 2,
      trader: "DIK",
      amount: "$8,460.00",
      date: "05 APR 2025",
      country: "AT",
      category: "Crypto",
      zIndex: 20,
      rotation: -15,
      translateX: -80,
    },
    {
      id: 3,
      trader: "JHAMMAD ASAD SLAM",
      amount: "$309.55",
      date: "23 MAR 2025",
      country: "PK",
      category: "Commodities",
      zIndex: 10,
      rotation: 15,
      translateX: 80,
    },
    {
      id: 4,
      trader: "SARAH MARTINEZ",
      amount: "$12,450.00",
      date: "04 APR 2025",
      country: "US",
      category: "Forex",
      zIndex: 30,
      rotation: 0,
      translateX: 0,
    },
    {
      id: 5,
      trader: "MICHAEL RODRIGUEZ",
      amount: "$8,920.00",
      date: "03 APR 2025",
      country: "ES",
      category: "Crypto",
      zIndex: 20,
      rotation: -15,
      translateX: -80,
    },
    {
      id: 6,
      trader: "ALEX KIM",
      amount: "$15,680.00",
      date: "02 APR 2025",
      country: "KR",
      category: "Commodities",
      zIndex: 10,
      rotation: 15,
      translateX: 80,
    },
  ]

  // Auto-rotate certificates every 3 seconds with smooth transitions
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 3) % payoutCertificates.length)
    }, 3000)

    return () => clearInterval(interval)
  }, [payoutCertificates.length])

  // Get current set of 3 certificates
  const getCurrentCertificates = () => {
    const startIndex = currentIndex
    return [
      payoutCertificates[startIndex],
      payoutCertificates[(startIndex + 1) % payoutCertificates.length],
      payoutCertificates[(startIndex + 2) % payoutCertificates.length],
    ]
  }

  return (
    <section className="py-24 md:py-32 relative overflow-hidden">
      {/* Background Elements - Match hero section */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-slate-950 dark:via-gray-900 dark:to-slate-950" />
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-[15%] left-[5%] w-64 h-64 rounded-full bg-gradient-to-r from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10 blur-3xl animate-pulse" />
        <div
          className="absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-r from-indigo-500/5 to-purple-500/5 dark:from-indigo-500/10 dark:to-purple-500/10 blur-3xl animate-pulse"
          style={{ animationDelay: "1s" }}
        />
      </div>

      <div className="max-w-7xl mx-auto px-8 md:px-12 lg:px-16 relative z-10">
        {/* Header */}
        <div className="text-center mb-20">
          <Badge className="mb-6 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-400/20 px-4 py-2">
            <DollarSign className="w-4 h-4 mr-2" />
            Real Payouts
          </Badge>
          <h2 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 leading-tight">
            <span className="bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-400 dark:via-cyan-400 dark:to-indigo-400 bg-clip-text text-transparent">
              Real Profits
            </span>{" "}
            Real Traders
          </h2>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 leading-relaxed max-w-3xl mx-auto">
            See actual payouts from our funded traders. These are real profits earned by real people using our capital.
          </p>
        </div>

        {/* Professional Certificate Showcase */}
        <div className="mb-20">
          <div className="text-center mb-16">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Profit Share Certificates
            </h3>
            <p className="text-lg text-gray-700 dark:text-white/70">
              Real profit share certificates from our funded traders
            </p>
          </div>

          {/* Fanned Certificate Layout */}
          <div className="relative h-[600px] flex items-center justify-center">
            {getCurrentCertificates().map((certificate, index) => (
              <div
                key={`${certificate.id}-${currentIndex}`}
                className="absolute group cursor-pointer transition-all duration-1000 hover:z-50 hover:scale-110 animate-float"
                style={{
                  zIndex: certificate.zIndex,
                  transform: `translateX(${certificate.translateX}px) rotate(${certificate.rotation}deg)`,
                  animationDelay: `${index * 0.5}s`,
                  '--rotation': `${certificate.rotation}deg`,
                  '--translateX': `${certificate.translateX}px`,
                } as React.CSSProperties}
              >
                <div className="relative w-80 h-96 bg-gradient-to-br from-blue-900 via-cyan-900 to-indigo-900 rounded-2xl shadow-2xl border border-blue-400/20 overflow-hidden transform-gpu hover:rotate-y-12 hover:scale-105 transition-all duration-700 animate-card-float">
                  {/* Animated Starry Background */}
                  <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.1)_1px,transparent_1px)] bg-[length:20px_20px] animate-twinkle" />
                  
                  {/* Animated Mountain Silhouette */}
                  <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-gray-800/30 to-transparent">
                    <div className="absolute bottom-0 left-0 w-full h-full animate-slide-up">
                      <svg viewBox="0 0 320 64" className="w-full h-full">
                        <path d="M0,64 L80,40 L160,50 L240,35 L320,45 L320,64 Z" fill="rgba(255,255,255,0.1)" />
                      </svg>
                    </div>
                  </div>

                  {/* Certificate Content */}
                  <div className="relative z-10 p-8 h-full flex flex-col">
                    {/* Header */}
                    <div className="text-center mb-6">
                      <h3 className="text-2xl font-bold text-cyan-300 mb-2">PROFIT SHARE</h3>
                      <div className="flex items-center justify-center gap-3 mb-4">
                        <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center">
                          <span className="text-white font-bold text-lg">A</span>
                        </div>
                        <div className="text-center">
                          <div className="text-cyan-300 text-sm font-semibold">APEX CAPITAL</div>
                        </div>
                      </div>
                    </div>

                    {/* Recipient */}
                    <div className="text-center mb-6">
                      <p className="text-cyan-200 text-sm mb-1">Delightedly presented for:</p>
                      <p className="text-white font-bold text-lg">{certificate.trader}</p>
                    </div>

                    {/* Amount */}
                    <div className="text-center mb-6 flex-1 flex items-center justify-center">
                      <div>
                        <p className="text-cyan-200 text-sm mb-2">Your share:</p>
                        <p className="text-3xl font-bold text-cyan-300">{certificate.amount}</p>
                      </div>
                    </div>

                    {/* Footer */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
                          <span className="text-white text-xs font-bold">{certificate.country}</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-cyan-300 text-sm font-semibold">Ethan Carter</p>
                        <p className="text-cyan-200 text-xs">CEO</p>
                      </div>
                    </div>

                    {/* Date */}
                    <div className="text-center mt-4">
                      <p className="text-cyan-200 text-sm">{certificate.date}</p>
                    </div>
                  </div>

                  {/* Hover Effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600/0 to-cyan-600/0 group-hover:from-blue-600/20 group-hover:to-cyan-600/20 transition-all duration-500 rounded-2xl" />
                </div>
              </div>
            ))}
          </div>

          {/* Certificate Info */}
          <div className="text-center mt-12">
            <p className="text-gray-600 dark:text-white/60 text-lg">
              These are real profit share certificates issued to our funded traders
            </p>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center mt-16">
          <Button className="bg-gradient-to-r from-blue-600 via-cyan-600 to-indigo-600 dark:from-blue-500 dark:via-cyan-500 dark:to-indigo-500 text-white hover:from-blue-700 hover:via-cyan-700 hover:to-indigo-700 dark:hover:from-blue-600 dark:hover:via-cyan-600 dark:hover:to-indigo-600 px-8 py-4 text-lg hover:scale-105 transition-all duration-300 hover:shadow-lg">
            <Zap className="w-5 h-5 mr-2" />
            Start Earning Like These Traders
          </Button>
        </div>
      </div>

      {/* Custom CSS Animations */}
      <style jsx>{`
        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }
        
        @keyframes card-float {
          0%, 100% {
            transform: translateY(0px) rotate(var(--rotation)) translateX(var(--translateX));
          }
          25% {
            transform: translateY(-5px) rotate(calc(var(--rotation) + 2deg)) translateX(calc(var(--translateX) + 5px));
          }
          50% {
            transform: translateY(-10px) rotate(var(--rotation)) translateX(var(--translateX));
          }
          75% {
            transform: translateY(-5px) rotate(calc(var(--rotation) - 2deg)) translateX(calc(var(--translateX) - 5px));
          }
        }
        
        @keyframes twinkle {
          0%, 100% {
            opacity: 0.3;
          }
          50% {
            opacity: 0.8;
          }
        }
        
        @keyframes slide-up {
          0% {
            transform: translateY(100%);
            opacity: 0;
          }
          100% {
            transform: translateY(0);
            opacity: 1;
          }
        }
        
        .animate-float {
          animation: float 3s ease-in-out infinite;
        }
        
        .animate-card-float {
          animation: card-float 4s ease-in-out infinite;
        }
        
        .animate-twinkle {
          animation: twinkle 2s ease-in-out infinite;
        }
        
        .animate-slide-up {
          animation: slide-up 1s ease-out;
        }
        
        .hover\\:rotate-y-12:hover {
          transform: rotateY(12deg);
        }
      `}</style>
    </section>
  )
} 