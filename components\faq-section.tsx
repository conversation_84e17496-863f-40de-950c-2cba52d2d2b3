"use client"

import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Card, CardContent } from "@/components/ui/card"
import { ChevronDown, HelpCircle, MessageCircle, Phone, Mail } from "lucide-react"
import { useState } from "react"
import { useLanguage } from "@/contexts/language-context"

export default function FAQSection() {
  const { t } = useLanguage()
  const [openItems, setOpenItems] = useState<number[]>([0])

  const toggleItem = (index: number) => {
    setOpenItems((prev) => (prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index]))
  }

  const faqs = [
    {
      question: "How does the evaluation process work?",
      answer:
        "Our evaluation process is straightforward: pass our trading challenge by meeting the profit target while staying within risk parameters. Once you pass, you'll receive funding to trade with our capital and keep up to 80% of the profits you generate.",
    },
    {
      question: "What is the maximum funding available?",
      answer:
        "We offer funding up to $400,000. You can start with smaller account sizes like $10K, $25K, or $50K and scale up based on your performance and consistency.",
    },
    {
      question: "Are there any time limits for the evaluation?",
      answer:
        "No, we don't impose strict time limits on our evaluations. Take the time you need to trade consistently and meet the requirements. However, we do require a minimum number of trading days to ensure consistency.",
    },
    {
      question: "Can I use Expert Advisors (EAs) and automated trading?",
      answer:
        "Yes, we allow Expert Advisors and automated trading strategies. We believe in giving traders the freedom to use the tools and strategies they're most comfortable with.",
    },
    {
      question: "What happens if I fail the evaluation?",
      answer:
        "If you don't pass the evaluation on your first attempt, you can retake it. We also offer free retakes on certain account types. Our goal is to help you succeed, not to make the process unnecessarily difficult.",
    },
    {
      question: "How quickly can I get funded?",
      answer:
        "Once you successfully complete the evaluation, funding typically takes 1-3 business days. We verify your trading performance and then provide you with your funded account credentials.",
    },
    {
      question: "What trading platforms do you support?",
      answer:
        "We primarily support MetaTrader 4 (MT4) and MetaTrader 5 (MT5). These platforms are industry standards and offer all the tools and features professional traders need.",
    },
    {
      question: "Is there ongoing support after I get funded?",
      answer:
        "We provide 24/7 support to all our funded traders. This includes technical support, account management assistance, and access to our trading community and educational resources.",
    },
  ]

  const contactMethods = [
    {
      icon: MessageCircle,
      title: "Live Chat",
      description: "Get instant answers to your questions",
      color: "from-blue-500 to-cyan-500",
      bgColor: "from-blue-500/10 to-cyan-500/10",
      borderColor: "border-blue-200 dark:border-blue-400/20",
    },
    {
      icon: Mail,
      title: "Email Support",
      description: "Detailed responses within 24 hours",
      color: "from-indigo-500 to-purple-500",
      bgColor: "from-indigo-500/10 to-purple-500/10",
      borderColor: "border-indigo-200 dark:border-indigo-400/20",
    },
    {
      icon: Phone,
      title: "Phone Support",
      description: "Speak directly with our experts",
      color: "from-cyan-500 to-teal-500",
      bgColor: "from-cyan-500/10 to-teal-500/10",
      borderColor: "border-cyan-200 dark:border-cyan-400/20",
    },
  ]

  return (
    <section className="py-24 md:py-32 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-blue-50/20 to-transparent dark:via-blue-950/10" />
      <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 left-1/4 w-80 h-80 bg-gradient-to-r from-indigo-500/5 to-purple-500/5 dark:from-indigo-500/10 dark:to-purple-500/10 rounded-full blur-3xl" />

      <div className="max-w-4xl mx-auto px-8 md:px-12 lg:px-16 relative">
        {/* Section Header */}
        <div className="text-center mb-20 md:mb-28">
          <Badge
            variant="outline"
            className="mb-6 text-sm font-light border-blue-200 dark:border-blue-400/20 text-blue-600 dark:text-blue-400 px-4 py-2"
          >
            <HelpCircle className="w-4 h-4 mr-2" />
            {t("faq.badge")}
          </Badge>

          <h2 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 leading-tight">
            <span className="text-gray-900 dark:text-white">Frequently Asked</span>{" "}
            <span className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400 bg-clip-text text-transparent">
              Questions
            </span>
          </h2>

          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 max-w-3xl mx-auto leading-relaxed">
            Get answers to the most common questions about our prop trading program and evaluation process.
          </p>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4 md:space-y-6 mb-20 md:mb-28">
          {faqs.map((faq, index) => (
            <Collapsible key={index} open={openItems.includes(index)} onOpenChange={() => toggleItem(index)}>
              <Card className="group bg-gradient-to-br from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 shadow-sm hover:shadow-lg transition-all duration-300">
                <CollapsibleTrigger className="w-full">
                  <CardContent className="p-6 md:p-8">
                    <div className="flex items-center justify-between text-left">
                      <h3 className="text-lg md:text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300 pr-4">
                        {faq.question}
                      </h3>
                      <ChevronDown
                        className={`w-5 h-5 text-blue-600 dark:text-blue-400 transition-transform duration-300 flex-shrink-0 ${
                          openItems.includes(index) ? "rotate-180" : ""
                        }`}
                      />
                    </div>
                  </CardContent>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <CardContent className="px-6 md:px-8 pb-6 md:pb-8 pt-0">
                    <div className="border-t border-blue-200 dark:border-blue-400/20 pt-6">
                      <p className="text-gray-700 dark:text-white/80 leading-relaxed">{faq.answer}</p>
                    </div>
                  </CardContent>
                </CollapsibleContent>
              </Card>
            </Collapsible>
          ))}
        </div>

        {/* Contact Methods */}
        <div className="text-center">
          <h3 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-8 md:mb-12">
            Still Have Questions?
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
            {contactMethods.map((method, index) => {
              const IconComponent = method.icon
              return (
                <Card
                  key={index}
                  className={`group bg-gradient-to-br ${method.bgColor} dark:${method.bgColor.replace("/10", "/15")} backdrop-blur-sm border ${method.borderColor} shadow-sm hover:shadow-xl transition-all duration-500 hover:scale-105 cursor-pointer`}
                >
                  <CardContent className="p-6 md:p-8 text-center">
                    <div
                      className={`w-16 h-16 mx-auto rounded-2xl bg-gradient-to-r ${method.color} flex items-center justify-center mb-6 group-hover:scale-110 group-hover:rotate-12 transition-all duration-500 shadow-lg`}
                    >
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>

                    <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                      {method.title}
                    </h4>

                    <p className="text-gray-600 dark:text-white/70 leading-relaxed">{method.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </div>
    </section>
  )
}
