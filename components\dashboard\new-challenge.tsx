"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  TrendingUp,
  Target,
  Shield,
  Clock,
  DollarSign,
  Zap,
  Check,
  Star,
  CreditCard,
  Globe,
  ArrowRight,
  Monitor,
  Smartphone,
  Zap as Lightning,
  Target as Bullseye,
  Play,
  Layers,
} from "lucide-react"
import { useState } from "react"

export default function NewChallenge() {
  const [selectedBalance, setSelectedBalance] = useState(25000)
  const [selectedCurrency, setSelectedCurrency] = useState("USD")
  const [selectedPlatform, setSelectedPlatform] = useState("MT5")
  const [selectedChallengeType, setSelectedChallengeType] = useState("Phase 1")

  const currencies = [
    { code: "USD", flag: "🇺🇸", name: "US Dollar", flagUrl: "/images/flags/us.png" },
    { code: "EUR", flag: "🇪🇺", name: "<PERSON>", flagUrl: "/images/flags/eur.png" },
    { code: "GBP", flag: "🇬🇧", name: "British Pound", flagUrl: "/images/flags/gbp.png" },
    { code: "CAD", flag: "🇨🇦", name: "Canadian Dollar", flagUrl: "/images/flags/cad.png" },
    { code: "AUD", flag: "🇦🇺", name: "Australian Dollar", flagUrl: "/images/flags/aud.png" },
    { code: "CHF", flag: "🇨🇭", name: "Swiss Franc", flagUrl: "/images/flags/chf.png" },
    { code: "JPY", flag: "🇯🇵", name: "Japanese Yen", flagUrl: "/images/flags/jpy.png" },
    { code: "NZD", flag: "🇳🇿", name: "New Zealand Dollar", flagUrl: "/images/flags/nzd.png" },
  ]

  const tradingPlatforms = [
    {
      id: "MT5",
      name: "MetaTrader 5",
      description: "Advanced trading platform with superior tools",
      logo: "🟢",
      features: ["Advanced charting", "Multiple timeframes", "EA support", "Mobile trading"],
      popular: true,
    },
    {
      id: "MT4",
      name: "MetaTrader 4",
      description: "Classic trading platform with proven reliability",
      logo: "🔵",
      features: ["Simple interface", "Wide EA support", "Stable performance", "Mobile app"],
      popular: false,
    },
  ]

  const challengeTypes = [
    {
      id: "Phase 1",
      name: "Phase 1 Challenge",
      description: "Complete the initial evaluation phase",
      icon: Bullseye,
      color: "blue",
      duration: "Unlimited",
      target: "8% profit target",
      drawdown: "5% daily, 10% total",
      popular: true,
    },
    {
      id: "Phase 2",
      name: "Phase 2 Verification",
      description: "Verify your skills in the second phase",
      icon: Layers,
      color: "purple",
      duration: "Unlimited",
      target: "5% profit target",
      drawdown: "5% daily, 10% total",
      popular: false,
    },
    {
      id: "Instant",
      name: "Instant Funding",
      description: "Skip evaluation and start trading immediately",
      icon: Lightning,
      color: "orange",
      duration: "Immediate",
      target: "No target",
      drawdown: "5% daily, 10% total",
      popular: false,
    },
    {
      id: "HFT",
      name: "High-Frequency Trading",
      description: "Specialized for algorithmic trading strategies",
      icon: Monitor,
      color: "green",
      duration: "Unlimited",
      target: "8% profit target",
      drawdown: "3% daily, 8% total",
      popular: false,
    },
  ]

  const balanceOptions = [
    { amount: 10000, price: 59, popular: false },
    { amount: 25000, price: 99, popular: true },
    { amount: 50000, price: 199, popular: false },
    { amount: 100000, price: 399, popular: false },
    { amount: 200000, price: 799, popular: false },
  ]

  const challengeFeatures = [
    {
      title: "Two-Step Evaluation",
      description: "Complete Phase 1 and Phase 2 to get funded",
      icon: Target,
      color: "blue",
    },
    {
      title: "No Time Limits",
      description: "Take as long as you need to complete each phase",
      icon: Clock,
      color: "blue",
    },
    {
      title: "90% Profit Split",
      description: "Keep up to 90% of your trading profits",
      icon: DollarSign,
      color: "purple",
    },
    {
      title: "Risk Management",
      description: "5% daily drawdown, 10% maximum drawdown",
      icon: Shield,
      color: "orange",
    },
    {
      title: "Fast Funding",
      description: "Get funded within 24-48 hours after passing",
      icon: Zap,
      color: "blue",
    },
    {
      title: "Scaling Plan",
      description: "Scale up to $400K with our unlimited scaling",
      icon: TrendingUp,
      color: "blue",
    },
  ]

  const tradingObjectives = [
    {
      parameter: "Profit Target",
      phase1: "8%",
      phase2: "5%",
      funded: "No target",
    },
    {
      parameter: "Maximum Trading Days",
      phase1: "4 days",
      phase2: "4 days",
      funded: "5 days",
    },
    {
      parameter: "Maximum Daily Loss",
      phase1: "5%",
      phase2: "5%",
      funded: "5%",
    },
    {
      parameter: "Maximum Loss",
      phase1: "10%",
      phase2: "10%",
      funded: "10%",
    },
    {
      parameter: "Trading Period",
      phase1: "Unlimited",
      phase2: "Unlimited",
      funded: "Unlimited",
    },
  ]

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: "from-blue-500 to-cyan-500",
      purple: "from-purple-500 to-pink-500",
      orange: "from-orange-500 to-amber-500",
      green: "from-green-500 to-emerald-500",
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.blue
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-8 shadow-xl relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10" />
        <div className="relative z-10">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Start Your Trading Challenge</h1>
          <p className="text-lg text-gray-700 dark:text-white/70 mb-6">
            Choose your account size and begin your journey to becoming a funded trader
          </p>
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-blue-500" />
              <span className="text-sm text-gray-600 dark:text-white/60">No time limits</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-blue-500" />
              <span className="text-sm text-gray-600 dark:text-white/60">90% profit split</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-purple-500" />
              <span className="text-sm text-gray-600 dark:text-white/60">Fast funding</span>
            </div>
          </div>
        </div>
      </div>

      {/* Currency Selection */}
      <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Select Currency</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
          {currencies.map((currency) => (
            <button
              key={currency.code}
              onClick={() => setSelectedCurrency(currency.code)}
              className={`p-4 rounded-2xl border-2 transition-all duration-300 hover:scale-105 ${
                selectedCurrency === currency.code
                  ? "bg-blue-50 dark:bg-blue-500/10 text-blue-700 dark:text-blue-300 border-blue-300 dark:border-blue-400/40 shadow-lg"
                  : "bg-white/50 dark:bg-white/5 text-gray-700 dark:text-white/70 border-gray-200 dark:border-white/20 hover:border-blue-200 dark:hover:border-blue-400/20"
              }`}
            >
              <div className="text-2xl mb-2">{currency.flag}</div>
              <div className="font-bold text-lg">{currency.code}</div>
              <div className="text-xs opacity-70">{currency.name}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Trading Platform Selection */}
      <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Select Trading Platform</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {tradingPlatforms.map((platform) => (
            <div key={platform.id} className="relative">
              {platform.popular && (
                <div className="absolute -top-3 left-1/2 -translate-x-1/2 z-10">
                  <Badge className="bg-green-600 dark:bg-green-500 text-white text-xs px-3 py-1 shadow-lg">
                    <Star className="w-3 h-3 mr-1" />
                    RECOMMENDED
                  </Badge>
                </div>
              )}
              <button
                onClick={() => setSelectedPlatform(platform.id)}
                className={`w-full p-6 rounded-2xl border-2 transition-all duration-300 hover:scale-105 ${
                  selectedPlatform === platform.id
                    ? "bg-blue-50 dark:bg-blue-500/10 text-blue-700 dark:text-blue-300 border-blue-300 dark:border-blue-400/40 shadow-lg"
                    : "bg-white/50 dark:bg-white/5 text-gray-700 dark:text-white/70 border-gray-200 dark:border-white/20 hover:border-blue-200 dark:hover:border-blue-400/20"
                } ${platform.popular ? "ring-2 ring-green-400/20 dark:ring-green-400/30" : ""}`}
              >
                <div className="flex items-center gap-4 mb-4">
                  <div className="text-3xl">{platform.logo}</div>
                  <div>
                    <h4 className="text-xl font-bold">{platform.name}</h4>
                    <p className="text-sm opacity-70">{platform.description}</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  {platform.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-1">
                      <Check className="w-3 h-3 text-green-600 dark:text-green-400" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Challenge Type Selection */}
      <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Select Challenge Type</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {challengeTypes.map((challenge) => {
            const IconComponent = challenge.icon
            const gradientClass = getColorClasses(challenge.color)

            return (
              <div key={challenge.id} className="relative">
                {challenge.popular && (
                  <div className="absolute -top-3 left-1/2 -translate-x-1/2 z-10">
                    <Badge className="bg-blue-600 dark:bg-blue-500 text-white text-xs px-3 py-1 shadow-lg">
                      <Star className="w-3 h-3 mr-1" />
                      POPULAR
                    </Badge>
                  </div>
                )}
                <button
                  onClick={() => setSelectedChallengeType(challenge.id)}
                  className={`w-full p-6 rounded-2xl border-2 transition-all duration-300 hover:scale-105 ${
                    selectedChallengeType === challenge.id
                      ? "bg-blue-50 dark:bg-blue-500/10 text-blue-700 dark:text-blue-300 border-blue-300 dark:border-blue-400/40 shadow-lg"
                      : "bg-white/50 dark:bg-white/5 text-gray-700 dark:text-white/70 border-gray-200 dark:border-white/20 hover:border-blue-200 dark:hover:border-blue-400/20"
                  } ${challenge.popular ? "ring-2 ring-blue-400/20 dark:ring-blue-400/30" : ""}`}
                >
                  <div className="flex items-center gap-3 mb-4">
                    <div className={`w-12 h-12 rounded-2xl bg-gradient-to-r ${gradientClass} flex items-center justify-center shadow-lg`}>
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h4 className="text-lg font-bold">{challenge.name}</h4>
                      <p className="text-sm opacity-70">{challenge.description}</p>
                    </div>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="opacity-70">Duration:</span>
                      <span className="font-semibold">{challenge.duration}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="opacity-70">Target:</span>
                      <span className="font-semibold">{challenge.target}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="opacity-70">Drawdown:</span>
                      <span className="font-semibold">{challenge.drawdown}</span>
                    </div>
                  </div>
                </button>
              </div>
            )
          })}
        </div>
      </div>

      {/* Account Size Selection */}
      <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Choose Account Size</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6">
          {balanceOptions.map((option) => (
            <div key={option.amount} className="relative">
              {option.popular && (
                <div className="absolute -top-3 left-1/2 -translate-x-1/2 z-10">
                  <Badge className="bg-blue-600 dark:bg-blue-500 text-white text-xs px-3 py-1 shadow-lg">
                    <Star className="w-3 h-3 mr-1" />
                    POPULAR
                  </Badge>
                </div>
              )}
              <button
                onClick={() => setSelectedBalance(option.amount)}
                className={`w-full p-6 rounded-2xl border-2 transition-all duration-300 hover:scale-105 ${
                  selectedBalance === option.amount
                    ? "bg-blue-50 dark:bg-blue-500/10 text-blue-700 dark:text-blue-300 border-blue-300 dark:border-blue-400/40 shadow-lg"
                    : "bg-white/50 dark:bg-white/5 text-gray-700 dark:text-white/70 border-gray-200 dark:border-white/20 hover:border-blue-200 dark:hover:border-blue-400/20"
                } ${option.popular ? "ring-2 ring-blue-400/20 dark:ring-blue-400/30" : ""}`}
              >
                <div className="text-2xl font-bold mb-2">${option.amount.toLocaleString()}</div>
                <div className="text-lg font-semibold mb-2">${option.price}</div>
                <div className="text-xs opacity-70">One-time fee</div>
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Trading Objectives */}
      <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Trading Objectives</h3>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200 dark:border-white/10">
                <th className="text-left py-4 px-4 font-semibold text-gray-700 dark:text-white/70">Parameter</th>
                <th className="text-center py-4 px-4 font-semibold text-gray-700 dark:text-white/70">
                  Phase 1<div className="text-xs font-normal text-blue-600 dark:text-blue-400">Challenge</div>
                </th>
                <th className="text-center py-4 px-4 font-semibold text-gray-700 dark:text-white/70">
                  Phase 2<div className="text-xs font-normal text-blue-600 dark:text-blue-400">Verification</div>
                </th>
                <th className="text-center py-4 px-4 font-semibold text-gray-700 dark:text-white/70">
                  Funded
                  <div className="text-xs font-normal text-purple-600 dark:text-purple-400">Live Trading</div>
                </th>
              </tr>
            </thead>
            <tbody>
              {tradingObjectives.map((objective, index) => (
                <tr
                  key={index}
                  className="border-b border-gray-100 dark:border-white/5 hover:bg-gray-50/50 dark:hover:bg-white/5 transition-colors duration-200"
                >
                  <td className="py-4 px-4 font-medium text-gray-900 dark:text-white">{objective.parameter}</td>
                  <td className="py-4 px-4 text-center font-bold text-gray-900 dark:text-white">{objective.phase1}</td>
                  <td className="py-4 px-4 text-center font-bold text-gray-900 dark:text-white">{objective.phase2}</td>
                  <td className="py-4 px-4 text-center font-bold text-gray-900 dark:text-white">{objective.funded}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {challengeFeatures.map((feature, index) => {
          const IconComponent = feature.icon
          const gradientClass = getColorClasses(feature.color)

          return (
            <div
              key={index}
              className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] group"
            >
              <div className="flex items-center gap-4 mb-4">
                <div
                  className={`w-12 h-12 rounded-2xl bg-gradient-to-r ${gradientClass} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}
                >
                  <IconComponent className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h4 className="text-lg font-bold text-gray-900 dark:text-white">{feature.title}</h4>
                </div>
              </div>
              <p className="text-gray-700 dark:text-white/70">{feature.description}</p>
            </div>
          )
        })}
      </div>

      {/* Purchase Summary */}
      <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-8 shadow-xl">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Order Summary</h3>
          <Badge className="bg-blue-600 dark:bg-blue-500 text-white">
            <Check className="w-4 h-4 mr-1" />
            Best Value
          </Badge>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-700 dark:text-white/70">Account Size:</span>
              <span className="font-bold text-gray-900 dark:text-white">
                ${selectedBalance.toLocaleString()} {selectedCurrency}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700 dark:text-white/70">Trading Platform:</span>
              <span className="font-bold text-gray-900 dark:text-white">
                {tradingPlatforms.find((p) => p.id === selectedPlatform)?.name}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700 dark:text-white/70">Challenge Type:</span>
              <span className="font-bold text-gray-900 dark:text-white">
                {challengeTypes.find((c) => c.id === selectedChallengeType)?.name}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700 dark:text-white/70">Challenge Fee:</span>
              <span className="font-bold text-gray-900 dark:text-white">
                ${balanceOptions.find((opt) => opt.amount === selectedBalance)?.price} {selectedCurrency}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-700 dark:text-white/70">Profit Split:</span>
              <span className="font-bold text-blue-600 dark:text-blue-400">Up to 90%</span>
            </div>
            <div className="flex items-center justify-between border-t border-gray-200 dark:border-white/10 pt-4">
              <span className="text-lg font-bold text-gray-900 dark:text-white">Total:</span>
              <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                ${balanceOptions.find((opt) => opt.amount === selectedBalance)?.price} {selectedCurrency}
              </span>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-white/60">
              <Check className="w-4 h-4 text-blue-600 dark:text-blue-400" />
              <span>Refundable after first payout</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-white/60">
              <Check className="w-4 h-4 text-blue-600 dark:text-blue-400" />
              <span>No time limits on any phase</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-white/60">
              <Check className="w-4 h-4 text-blue-600 dark:text-blue-400" />
              <span>24/7 support included</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-white/60">
              <Check className="w-4 h-4 text-blue-600 dark:text-blue-400" />
              <span>Unlimited scaling plan</span>
            </div>
          </div>
        </div>

        <div className="mt-8 flex flex-col sm:flex-row gap-4">
          <Button className="flex-1 bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600 py-4 text-lg hover:scale-[1.02] transition-all duration-300 hover:shadow-lg">
            <CreditCard className="w-5 h-5 mr-2" />
            Purchase Challenge
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
          <Button
            variant="outline"
            className="border-gray-300 dark:border-white/20 text-gray-700 dark:text-white/70 hover:bg-gray-50 dark:hover:bg-white/5 py-4 text-lg bg-transparent"
          >
            <Globe className="w-5 h-5 mr-2" />
            Pay with Crypto
          </Button>
        </div>
      </div>
    </div>
  )
}
