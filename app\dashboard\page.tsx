"use client"

import { useState } from "react"
import DashboardLayout from "@/components/dashboard/dashboard-layout"
import AccountOverview from "@/components/dashboard/account-overview"
import NewChallenge from "@/components/dashboard/new-challenge"
import Withdraw from "@/components/dashboard/withdraw"
import Referral from "@/components/dashboard/referral"
import AffiliateProgram from "@/components/dashboard/affiliate-program"
import Certificate from "@/components/dashboard/certificate"
import Transactions from "@/components/dashboard/transactions"
import MyRewards from "@/components/dashboard/my-rewards"
import TradingRules from "@/components/dashboard/trading-rules"
import TicketSystem from "@/components/dashboard/ticket-system"
import FAQs from "@/components/dashboard/faqs"
import Support from "@/components/dashboard/support"

export default function Dashboard() {
  const [activeTab, setActiveTab] = useState("overview")

  const renderContent = () => {
    switch (activeTab) {
      case "overview":
        return <AccountOverview />
      case "new-challenge":
        return <NewChallenge />
      case "withdraw":
        return <Withdraw />
      case "referral":
        return <Referral />
      case "affiliate":
        return <AffiliateProgram />
      case "certificate":
        return <Certificate />
      case "transactions":
        return <Transactions />
      case "rewards":
        return <MyRewards />
      case "trading-rules":
        return <TradingRules />
      case "ticket":
        return <TicketSystem />
      case "faqs":
        return <FAQs />
      case "support":
        return <Support />
      default:
        return <AccountOverview />
    }
  }

  return (
    <DashboardLayout activeTab={activeTab} onTabChange={setActiveTab}>
      {renderContent()}
    </DashboardLayout>
  )
}
