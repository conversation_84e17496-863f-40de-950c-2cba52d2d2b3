"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Users,
  DollarSign,
  Copy,
  Mail,
  MessageCircle,
  Facebook,
  Twitter,
  Linkedin,
  TrendingUp,
  Gift,
  Star,
  CheckCircle,
  Clock,
} from "lucide-react"
import { useState } from "react"

export default function Referral() {
  const [referralLink] = useState("https://apexcapital.com/ref/johndoe123")
  const [copied, setCopied] = useState(false)

  const referralStats = {
    totalReferrals: 24,
    activeReferrals: 18,
    totalEarnings: 1250.0,
    pendingEarnings: 320.0,
    conversionRate: 12.5,
    thisMonthReferrals: 6,
  }

  const commissionTiers = [
    {
      tier: "Bronze",
      referrals: "1-10",
      commission: "15%",
      color: "orange",
      current: false,
    },
    {
      tier: "Silver",
      referrals: "11-25",
      commission: "20%",
      color: "gray",
      current: true,
    },
    {
      tier: "Gold",
      referrals: "26-50",
      commission: "25%",
      color: "yellow",
      current: false,
    },
    {
      tier: "Platinum",
      referrals: "51+",
      commission: "30%",
      color: "purple",
      current: false,
    },
  ]

  const recentReferrals = [
    {
      id: "REF001",
      name: "Michael <PERSON>",
      email: "<EMAIL>",
      status: "active",
      joinDate: "2024-01-10",
      commission: 45.0,
      challengeType: "$25K Challenge",
    },
    {
      id: "REF002",
      name: "Sarah Williams",
      email: "<EMAIL>",
      status: "pending",
      joinDate: "2024-01-12",
      commission: 0.0,
      challengeType: "$50K Challenge",
    },
    {
      id: "REF003",
      name: "David Brown",
      email: "<EMAIL>",
      status: "completed",
      joinDate: "2024-01-08",
      commission: 75.0,
      challengeType: "$100K Challenge",
    },
  ]

  const copyToClipboard = () => {
    navigator.clipboard.writeText(referralLink)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
      case "pending":
        return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400"
      case "completed":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
      default:
        return "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <TrendingUp className="w-4 h-4" />
      case "pending":
        return <Clock className="w-4 h-4" />
      case "completed":
        return <CheckCircle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  const getTierColor = (color: string, current: boolean) => {
    const colorMap = {
      orange: current ? "from-orange-500 to-amber-500" : "from-orange-500/20 to-amber-500/20",
      gray: current ? "from-gray-500 to-slate-500" : "from-gray-500/20 to-slate-500/20",
      yellow: current ? "from-yellow-500 to-amber-500" : "from-yellow-500/20 to-amber-500/20",
      purple: current ? "from-purple-500 to-pink-500" : "from-purple-500/20 to-pink-500/20",
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.orange
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-8 shadow-xl relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10" />
        <div className="relative z-10">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Referral Program</h1>
          <p className="text-lg text-gray-700 dark:text-white/70 mb-6">
            Earn commissions by referring new traders to Apex Capital
          </p>
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-blue-500" />
              <span className="text-sm text-gray-600 dark:text-white/60">Up to 30% commission</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-blue-500" />
              <span className="text-sm text-gray-600 dark:text-white/60">Lifetime earnings</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-purple-500" />
              <span className="text-sm text-gray-600 dark:text-white/60">Real-time tracking</span>
            </div>
          </div>
        </div>
      </div>

      {/* Referral Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-6 shadow-xl">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg">
              <Users className="w-6 h-6 text-white" />
            </div>
            <Badge className="bg-blue-600 dark:bg-blue-500 text-white">
              +{referralStats.thisMonthReferrals} this month
            </Badge>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">{referralStats.totalReferrals}</div>
          <div className="text-sm text-gray-600 dark:text-white/60">Total Referrals</div>
        </div>

        <div className="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-6 shadow-xl">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg">
              <DollarSign className="w-6 h-6 text-white" />
            </div>
            <Badge className="bg-blue-600 dark:bg-blue-500 text-white">
              ${referralStats.pendingEarnings.toFixed(2)} pending
            </Badge>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            ${referralStats.totalEarnings.toFixed(2)}
          </div>
          <div className="text-sm text-gray-600 dark:text-white/60">Total Earnings</div>
        </div>

        <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20 backdrop-blur-sm border border-purple-200 dark:border-purple-400/20 rounded-3xl p-6 shadow-xl">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center shadow-lg">
              <TrendingUp className="w-6 h-6 text-white" />
            </div>
            <Badge className="bg-purple-600 dark:bg-purple-500 text-white">Excellent</Badge>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">{referralStats.conversionRate}%</div>
          <div className="text-sm text-gray-600 dark:text-white/60">Conversion Rate</div>
        </div>
      </div>

      {/* Referral Link */}
      <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Your Referral Link</h3>

        <div className="space-y-6">
          <div className="flex gap-4">
            <Input
              value={referralLink}
              readOnly
              className="flex-1 rounded-xl border-gray-200 dark:border-white/20 bg-white/50 dark:bg-white/5"
            />
            <Button
              onClick={copyToClipboard}
              className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600 px-6"
            >
              {copied ? <CheckCircle className="w-4 h-4 mr-2" /> : <Copy className="w-4 h-4 mr-2" />}
              {copied ? "Copied!" : "Copy"}
            </Button>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <Button
              variant="outline"
              className="border-gray-300 dark:border-white/20 text-gray-700 dark:text-white/70 hover:bg-gray-50 dark:hover:bg-white/5 bg-transparent"
            >
              <Mail className="w-4 h-4 mr-2" />
              Email
            </Button>
            <Button
              variant="outline"
              className="border-gray-300 dark:border-white/20 text-gray-700 dark:text-white/70 hover:bg-gray-50 dark:hover:bg-white/5 bg-transparent"
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              WhatsApp
            </Button>
            <Button
              variant="outline"
              className="border-gray-300 dark:border-white/20 text-gray-700 dark:text-white/70 hover:bg-gray-50 dark:hover:bg-white/5 bg-transparent"
            >
              <Facebook className="w-4 h-4 mr-2" />
              Facebook
            </Button>
            <Button
              variant="outline"
              className="border-gray-300 dark:border-white/20 text-gray-700 dark:text-white/70 hover:bg-gray-50 dark:hover:bg-white/5 bg-transparent"
            >
              <Twitter className="w-4 h-4 mr-2" />
              Twitter
            </Button>
            <Button
              variant="outline"
              className="border-gray-300 dark:border-white/20 text-gray-700 dark:text-white/70 hover:bg-gray-50 dark:hover:bg-white/5 bg-transparent"
            >
              <Linkedin className="w-4 h-4 mr-2" />
              LinkedIn
            </Button>
          </div>
        </div>
      </div>

      {/* Commission Tiers */}
      <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Commission Tiers</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {commissionTiers.map((tier, index) => (
            <div
              key={index}
              className={`relative p-6 rounded-2xl border-2 transition-all duration-300 hover:scale-105 ${
                tier.current
                  ? "bg-gradient-to-br from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 border-blue-300 dark:border-blue-400/40 shadow-lg"
                  : "bg-white/50 dark:bg-white/5 border-gray-200 dark:border-white/20"
              }`}
            >
              {tier.current && (
                <div className="absolute -top-3 left-1/2 -translate-x-1/2">
                  <Badge className="bg-blue-600 dark:bg-blue-500 text-white text-xs px-3 py-1 shadow-lg">
                    <Star className="w-3 h-3 mr-1" />
                    CURRENT
                  </Badge>
                </div>
              )}

              <div className="text-center">
                <div
                  className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${getTierColor(tier.color, tier.current)} flex items-center justify-center mx-auto mb-4 shadow-lg`}
                >
                  <Gift className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{tier.tier}</h4>
                <div className="text-sm text-gray-600 dark:text-white/60 mb-4">{tier.referrals} referrals</div>
                <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">{tier.commission}</div>
                <div className="text-sm text-gray-600 dark:text-white/60">Commission Rate</div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-8 p-6 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 rounded-2xl border border-blue-200 dark:border-blue-400/20">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
              <TrendingUp className="w-4 h-4 text-white" />
            </div>
            <div className="font-bold text-gray-900 dark:text-white">Next Tier Progress</div>
          </div>
          <div className="text-sm text-gray-600 dark:text-white/60 mb-3">
            You need {26 - referralStats.totalReferrals} more referrals to reach Gold tier (25% commission)
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
            <div
              className="bg-gradient-to-r from-blue-500 to-cyan-500 h-3 rounded-full transition-all duration-500"
              style={{ width: `${(referralStats.totalReferrals / 26) * 100}%` }}
            />
          </div>
          <div className="text-right text-sm text-gray-600 dark:text-white/60 mt-1">
            {Math.round((referralStats.totalReferrals / 26) * 100)}% Complete
          </div>
        </div>
      </div>

      {/* Recent Referrals */}
      <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Recent Referrals</h3>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200 dark:border-white/10">
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">ID</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Name</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Email</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Challenge</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Join Date</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Commission</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Status</th>
              </tr>
            </thead>
            <tbody>
              {recentReferrals.map((referral) => (
                <tr
                  key={referral.id}
                  className="border-b border-gray-100 dark:border-white/5 hover:bg-gray-50/50 dark:hover:bg-white/5 transition-colors duration-200"
                >
                  <td className="py-4 px-4 font-mono text-sm text-gray-900 dark:text-white">{referral.id}</td>
                  <td className="py-4 px-4 font-medium text-gray-900 dark:text-white">{referral.name}</td>
                  <td className="py-4 px-4 text-gray-700 dark:text-white/70">{referral.email}</td>
                  <td className="py-4 px-4 text-gray-700 dark:text-white/70">{referral.challengeType}</td>
                  <td className="py-4 px-4 text-gray-700 dark:text-white/70">{referral.joinDate}</td>
                  <td className="py-4 px-4 font-bold text-gray-900 dark:text-white">
                    ${referral.commission.toFixed(2)}
                  </td>
                  <td className="py-4 px-4">
                    <Badge className={getStatusColor(referral.status)}>
                      {getStatusIcon(referral.status)}
                      <span className="ml-1 capitalize">{referral.status}</span>
                    </Badge>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
