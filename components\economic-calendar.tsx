"use client"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Calendar, TrendingUp, AlertTriangle, Clock, Globe, Filter } from "lucide-react"
import { useState } from "react"

export default function EconomicCalendar() {
  const [selectedImpact, setSelectedImpact] = useState("all")
  const [selectedCurrency, setSelectedCurrency] = useState("all")

  const economicEvents = [
    {
      time: "08:30",
      currency: "USD",
      flag: "🇺🇸",
      event: "Non-Farm Payrolls",
      impact: "high",
      forecast: "185K",
      previous: "199K",
      actual: "216K",
      status: "released",
    },
    {
      time: "10:00",
      currency: "EUR",
      flag: "🇪🇺",
      event: "ECB Interest Rate Decision",
      impact: "high",
      forecast: "4.50%",
      previous: "4.50%",
      actual: "-",
      status: "upcoming",
    },
    {
      time: "12:30",
      currency: "GBP",
      flag: "🇬🇧",
      event: "GDP Growth Rate",
      impact: "medium",
      forecast: "0.2%",
      previous: "0.1%",
      actual: "-",
      status: "upcoming",
    },
    {
      time: "14:00",
      currency: "CAD",
      flag: "🇨🇦",
      event: "Bank of Canada Rate Decision",
      impact: "high",
      forecast: "5.00%",
      previous: "5.00%",
      actual: "-",
      status: "upcoming",
    },
    {
      time: "15:30",
      currency: "AUD",
      flag: "🇦🇺",
      event: "Employment Change",
      impact: "medium",
      forecast: "15.0K",
      previous: "64.1K",
      actual: "-",
      status: "upcoming",
    },
    {
      time: "16:00",
      currency: "JPY",
      flag: "🇯🇵",
      event: "Core CPI",
      impact: "medium",
      forecast: "2.8%",
      previous: "2.8%",
      actual: "-",
      status: "upcoming",
    },
  ]

  const currencies = [
    { code: "all", name: "All Currencies", flag: "🌍" },
    { code: "USD", name: "US Dollar", flag: "🇺🇸" },
    { code: "EUR", name: "Euro", flag: "🇪🇺" },
    { code: "GBP", name: "British Pound", flag: "🇬🇧" },
    { code: "CAD", name: "Canadian Dollar", flag: "🇨🇦" },
    { code: "AUD", name: "Australian Dollar", flag: "🇦🇺" },
    { code: "JPY", name: "Japanese Yen", flag: "🇯🇵" },
  ]

  const impactLevels = [
    { value: "all", label: "All Impact", color: "gray" },
    { value: "high", label: "High Impact", color: "red" },
    { value: "medium", label: "Medium Impact", color: "yellow" },
    { value: "low", label: "Low Impact", color: "blue" },
  ]

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case "high":
        return "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400"
      case "medium":
        return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400"
      case "low":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
      default:
        return "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "released":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
      case "upcoming":
        return "bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-400"
      default:
        return "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
    }
  }

  const filteredEvents = economicEvents.filter((event) => {
    const impactMatch = selectedImpact === "all" || event.impact === selectedImpact
    const currencyMatch = selectedCurrency === "all" || event.currency === selectedCurrency
    return impactMatch && currencyMatch
  })

  return (
    <section className="py-24 md:py-32 relative" aria-labelledby="calendar-heading">
      <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
        {/* Section Header */}
        <div className="mb-20 md:mb-28 max-w-4xl mx-auto text-center">
          <Badge
            variant="outline"
            className="mb-8 md:mb-12 text-sm font-light border-gray-300 dark:border-white/20 text-gray-600 dark:text-white/80 px-4 py-2 items-center"
          >
            <Calendar className="w-4 h-4 mr-2" />
            Economic Calendar
          </Badge>
          <h2 id="calendar-heading" className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 md:mb-12 leading-tight">
            Stay ahead with{" "}
            <span className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400 bg-clip-text text-transparent">
              market events
            </span>
          </h2>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 leading-relaxed">
            Track important economic events and news that can impact your trading decisions. Never miss a market-moving
            announcement.
          </p>
        </div>

        {/* Filters */}
        <div className="mb-12">
          <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
                <Filter className="w-5 h-5 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">Filter Events</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Impact Filter */}
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-white/70 mb-3 block">Impact Level</label>
                <div className="flex flex-wrap gap-2">
                  {impactLevels.map((level) => (
                    <button
                      key={level.value}
                      onClick={() => setSelectedImpact(level.value)}
                      className={`px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 hover:scale-105 ${
                        selectedImpact === level.value
                          ? "bg-blue-600 dark:bg-blue-500 text-white shadow-lg"
                          : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
                      }`}
                    >
                      {level.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Currency Filter */}
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-white/70 mb-3 block">Currency</label>
                <div className="flex flex-wrap gap-2">
                  {currencies.slice(0, 4).map((currency) => (
                    <button
                      key={currency.code}
                      onClick={() => setSelectedCurrency(currency.code)}
                      className={`px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 hover:scale-105 flex items-center gap-2 ${
                        selectedCurrency === currency.code
                          ? "bg-blue-600 dark:bg-blue-500 text-white shadow-lg"
                          : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
                      }`}
                    >
                      <span>{currency.flag}</span>
                      {currency.code}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Calendar Events */}
        <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg">
                <Calendar className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Today's Events</h3>
                <p className="text-sm text-gray-600 dark:text-white/60">
                  {filteredEvents.length} events • {new Date().toLocaleDateString()}
                </p>
              </div>
            </div>
            <Button className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600">
              <Globe className="w-4 h-4 mr-2" />
              View Full Calendar
            </Button>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-white/10">
                  <th className="text-left py-4 px-4 font-semibold text-gray-700 dark:text-white/70">Time</th>
                  <th className="text-left py-4 px-4 font-semibold text-gray-700 dark:text-white/70">Currency</th>
                  <th className="text-left py-4 px-4 font-semibold text-gray-700 dark:text-white/70">Event</th>
                  <th className="text-left py-4 px-4 font-semibold text-gray-700 dark:text-white/70">Impact</th>
                  <th className="text-left py-4 px-4 font-semibold text-gray-700 dark:text-white/70">Forecast</th>
                  <th className="text-left py-4 px-4 font-semibold text-gray-700 dark:text-white/70">Previous</th>
                  <th className="text-left py-4 px-4 font-semibold text-gray-700 dark:text-white/70">Actual</th>
                  <th className="text-left py-4 px-4 font-semibold text-gray-700 dark:text-white/70">Status</th>
                </tr>
              </thead>
              <tbody>
                {filteredEvents.map((event, index) => (
                  <tr
                    key={index}
                    className="border-b border-gray-100 dark:border-white/5 hover:bg-gray-50/50 dark:hover:bg-white/5 transition-colors duration-200"
                  >
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-gray-500 dark:text-white/50" />
                        <span className="font-mono text-sm text-gray-900 dark:text-white">{event.time}</span>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{event.flag}</span>
                        <span className="font-bold text-gray-900 dark:text-white">{event.currency}</span>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="font-medium text-gray-900 dark:text-white">{event.event}</div>
                    </td>
                    <td className="py-4 px-4">
                      <Badge className={getImpactColor(event.impact)}>
                        {event.impact === "high" && <AlertTriangle className="w-3 h-3 mr-1" />}
                        {event.impact === "medium" && <TrendingUp className="w-3 h-3 mr-1" />}
                        <span className="capitalize">{event.impact}</span>
                      </Badge>
                    </td>
                    <td className="py-4 px-4 text-gray-700 dark:text-white/70">{event.forecast}</td>
                    <td className="py-4 px-4 text-gray-700 dark:text-white/70">{event.previous}</td>
                    <td className="py-4 px-4">
                      <span
                        className={`font-bold ${
                          event.actual === "-"
                            ? "text-gray-500 dark:text-white/50"
                            : event.actual > event.forecast
                              ? "text-blue-600 dark:text-blue-400"
                              : "text-red-600 dark:text-red-400"
                        }`}
                      >
                        {event.actual}
                      </span>
                    </td>
                    <td className="py-4 px-4">
                      <Badge className={getStatusColor(event.status)}>
                        <span className="capitalize">{event.status}</span>
                      </Badge>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Market Impact Info */}
        <div className="mt-12">
          <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-8 shadow-xl relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10" />
            <div className="relative z-10">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Understanding Market Impact</h3>
                <p className="text-lg text-gray-700 dark:text-white/70">
                  Learn how different economic events can affect your trading positions
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-red-500 to-pink-500 flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <AlertTriangle className="w-8 h-8 text-white" />
                  </div>
                  <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">High Impact</h4>
                  <p className="text-sm text-gray-700 dark:text-white/70">
                    Major market movers that can cause significant volatility and price swings
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-yellow-500 to-orange-500 flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <TrendingUp className="w-8 h-8 text-white" />
                  </div>
                  <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">Medium Impact</h4>
                  <p className="text-sm text-gray-700 dark:text-white/70">
                    Moderate influence on markets with potential for directional moves
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <Clock className="w-8 h-8 text-white" />
                  </div>
                  <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">Low Impact</h4>
                  <p className="text-sm text-gray-700 dark:text-white/70">
                    Minor events with limited market impact but still worth monitoring
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
