"use client"

import type React from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  TrendingUp,
  BarChart3,
  Plus,
  Wallet,
  Users,
  Award,
  FileText,
  CreditCard,
  Gift,
  BookOpen,
  Ticket,
  HelpCircle,
  MessageSquare,
  LogOut,
  Settings,
  Bell,
  Search,
  Menu,
  X,
  ChevronRight,
  Crown,
  Star,
  Zap,
  Shield,
  ChevronDown,
  Globe,
  Smartphone,
  Sun,
  Moon,
  ArrowLeft,
} from "lucide-react"
import { useState, useEffect } from "react"
import Link from "next/link"
import LanguageSelector from "@/components/language-selector"
import { useLanguage } from "@/contexts/language-context"

interface DashboardLayoutProps {
  children: React.ReactNode
  activeTab: string
  onTabChange: (tab: string) => void
}

export default function DashboardLayout({ children, activeTab, onTabChange }: DashboardLayoutProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [isDarkMode, setIsDarkMode] = useState(true)
  const { t, isRTL } = useLanguage()

  // Check system preference on initial load
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Check localStorage first, then system preference
      const savedTheme = localStorage.getItem("theme")
      const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
      const shouldUseDark = savedTheme ? savedTheme === "dark" : prefersDark
      
      setIsDarkMode(shouldUseDark)
      // Apply initial theme
      if (shouldUseDark) {
        document.documentElement.classList.add("dark")
      } else {
        document.documentElement.classList.remove("dark")
      }
    }
  }, [])

  const toggleTheme = () => {
    const newDarkMode = !isDarkMode
    setIsDarkMode(newDarkMode)
    document.documentElement.classList.toggle("dark")
    // Save theme preference to localStorage
    localStorage.setItem("theme", newDarkMode ? "dark" : "light")
  }

  const menuItems = [
    { 
      id: "overview", 
      label: t("dashboard.overview"), 
      icon: BarChart3,
      badge: null,
      description: "Account overview and analytics"
    },
    { 
      id: "new-challenge", 
      label: t("dashboard.newChallenge"), 
      icon: Plus,
      badge: null,
      description: "Start a new trading challenge"
    },
    { 
      id: "withdraw", 
      label: t("dashboard.withdraw"), 
      icon: Wallet,
      badge: null,
      description: "Withdraw your profits"
    },
    { 
      id: "referral", 
      label: t("dashboard.referral"), 
      icon: Users,
      badge: null,
      description: "Invite friends and earn bonuses"
    },
    { 
      id: "affiliate", 
      label: t("dashboard.affiliate"), 
      icon: Award,
      badge: null,
      description: "Affiliate program dashboard"
    },
    { 
      id: "certificate", 
      label: t("dashboard.certificate"), 
      icon: FileText,
      badge: null,
      description: "View your trading certificates"
    },
    { 
      id: "transactions", 
      label: t("dashboard.transactions"), 
      icon: CreditCard,
      badge: null,
      description: "Transaction history"
    },
    { 
      id: "rewards", 
      label: t("dashboard.rewards"), 
      icon: Gift,
      badge: null,
      description: "Claim your rewards"
    },
    { 
      id: "trading-rules", 
      label: t("dashboard.tradingRules"), 
      icon: BookOpen,
      badge: null,
      description: "Trading rules and guidelines"
    },
    { 
      id: "ticket", 
      label: t("dashboard.ticket"), 
      icon: Ticket,
      badge: "2",
      description: "Support tickets"
    },
    { 
      id: "faqs", 
      label: t("dashboard.faqs"), 
      icon: HelpCircle,
      badge: null,
      description: "Frequently asked questions"
    },
    { 
      id: "support", 
      label: t("dashboard.support"), 
      icon: MessageSquare,
      badge: null,
      description: "Get help and support"
    },
  ]

  const toolsServices = [
    { label: "Trading Tools", icon: BarChart3 },
    { label: "Analytics", icon: TrendingUp },
    { label: "Reports", icon: FileText },
  ]

  const supportItems = [
    { label: "Help Center", icon: HelpCircle },
    { label: "Contact Support", icon: MessageSquare },
    { label: "Documentation", icon: BookOpen },
  ]

  return (
    <div className={`min-h-screen bg-slate-50 dark:bg-slate-950 ${isRTL ? "rtl" : "ltr"}`}>
      {/* Mobile Header */}
      <div className="lg:hidden bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-slate-200/50 dark:border-slate-700/50 px-4 py-3 flex items-center justify-between sticky top-0 z-40">
        <div className="flex items-center gap-3">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setIsSidebarOpen(!isSidebarOpen)} 
            className="p-2 hover:bg-slate-100 dark:hover:bg-slate-800"
          >
            {isSidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center shadow-lg">
              <TrendingUp className="w-4 h-4 text-white" />
            </div>
            <span className="font-bold text-slate-900 dark:text-white">Apex Capital</span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <LanguageSelector />
          <Button variant="ghost" size="sm" className="p-2 relative hover:bg-slate-100 dark:hover:bg-slate-800">
            <Bell className="h-5 w-5" />
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center">
              <span className="text-xs text-white font-bold">3</span>
            </div>
          </Button>
        </div>
      </div>

      <div className="flex">
        {/* Compact Fixed Sidebar */}
        <div
          className={`${
            isSidebarOpen ? "translate-x-0" : "-translate-x-full"
          } lg:translate-x-0 fixed lg:sticky top-0 ${isRTL ? "right-0" : "left-0"} z-50 w-64 h-screen bg-white dark:bg-slate-900 border-r border-slate-200 dark:border-slate-700 shadow-xl transition-transform duration-300 ease-in-out lg:transition-none overflow-y-auto`}
        >
          {/* Sidebar Header */}
          <div className="hidden lg:flex items-center gap-3 p-4 border-b border-slate-200 dark:border-slate-700">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center shadow-lg">
              <TrendingUp className="w-4 h-4 text-white" />
            </div>
            <div>
              <h1 className="text-sm font-bold text-slate-900 dark:text-white">Apex Capital</h1>
              <p className="text-xs text-slate-600 dark:text-slate-400">Trading Dashboard</p>
            </div>
          </div>

          {/* Main Navigation */}
          <div className="p-4">
            {/* Primary Action Button */}
            <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg mb-6">
              <Plus className="w-4 h-4 mr-2" />
              New FTMO Challenge
            </Button>

            {/* Navigation Menu */}
            <nav className="space-y-1">
              {menuItems.slice(0, 6).map((item) => {
                const Icon = item.icon
                const isActive = activeTab === item.id
                return (
                  <button
                    key={item.id}
                    onClick={() => {
                      onTabChange(item.id)
                      setIsSidebarOpen(false)
                    }}
                    className={`w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 ${
                      isActive
                        ? "bg-slate-100 dark:bg-slate-800 text-slate-900 dark:text-white"
                        : "text-slate-600 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 hover:text-slate-900 dark:hover:text-white"
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="flex-1 text-left">{item.label}</span>
                    {item.badge && (
                      <Badge variant="secondary" className="text-xs bg-red-500 hover:bg-red-600">
                        {item.badge}
                      </Badge>
                    )}
                  </button>
                )
              })}
            </nav>

            {/* Tools & Services Section */}
            <div className="mt-6">
              <button className="w-full flex items-center justify-between px-3 py-2.5 rounded-lg text-sm font-medium text-slate-600 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 hover:text-slate-900 dark:hover:text-white transition-all duration-200">
                <div className="flex items-center gap-3">
                  <Shield className="w-4 h-4" />
                  <span>Tools & Services</span>
                </div>
                <ChevronDown className="w-4 h-4" />
              </button>
            </div>

            {/* Support Section */}
            <div className="mt-2">
              <button className="w-full flex items-center justify-between px-3 py-2.5 rounded-lg text-sm font-medium text-slate-600 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 hover:text-slate-900 dark:hover:text-white transition-all duration-200">
                <div className="flex items-center gap-3">
                  <HelpCircle className="w-4 h-4" />
                  <span>Support</span>
                </div>
                <ChevronDown className="w-4 h-4" />
              </button>
            </div>

            {/* Mobile App Section */}
            <div className="mt-2">
              <button className="w-full flex items-center justify-between px-3 py-2.5 rounded-lg text-sm font-medium text-slate-600 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 hover:text-slate-900 dark:hover:text-white transition-all duration-200">
                <div className="flex items-center gap-3">
                  <Smartphone className="w-4 h-4" />
                  <span>Mobile app</span>
                </div>
                <ChevronDown className="w-4 h-4" />
              </button>
            </div>

            {/* Settings & Preferences */}
            <div className="mt-6 space-y-1">
              <button 
                onClick={toggleTheme}
                className="w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium text-slate-600 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 hover:text-slate-900 dark:hover:text-white transition-all duration-200"
              >
                {isDarkMode ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
                <span>{isDarkMode ? "Light mode" : "Dark mode"}</span>
              </button>
              
              <Link href="/">
                <button className="w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium text-slate-600 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 hover:text-slate-900 dark:hover:text-white transition-all duration-200">
                  <ArrowLeft className="w-4 h-4" />
                  <span>Back to website</span>
                </button>
              </Link>

              <div className="flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium text-slate-600 dark:text-slate-300">
                <Globe className="w-4 h-4" />
                <span>English</span>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Sidebar Overlay */}
        {isSidebarOpen && (
          <div
            className="lg:hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
            onClick={() => setIsSidebarOpen(false)}
          />
        )}

        {/* Main Content Area */}
        <div className="flex-1 lg:ml-0 min-h-screen">
          {/* Desktop Header */}
          <div className="hidden lg:flex items-center justify-between bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border-b border-slate-200/50 dark:border-slate-700/50 px-8 py-4 sticky top-0 z-30">
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search dashboard..."
                  className="pl-10 pr-4 py-2.5 bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent w-80"
                />
              </div>
            </div>
            <div className="flex items-center gap-4">
              <LanguageSelector />
              <Button variant="ghost" size="sm" className="relative p-2 hover:bg-slate-100 dark:hover:bg-slate-800">
                <Bell className="w-5 h-5" />
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center">
                  <span className="text-xs text-white font-bold">3</span>
                </div>
              </Button>
              <div className="flex items-center gap-3">
                <div className="text-right">
                  <p className="text-sm font-semibold text-slate-900 dark:text-white">Badar</p>
                  <p className="text-xs text-slate-600 dark:text-slate-400">Premium Member</p>
                </div>
                <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 flex items-center justify-center text-white font-bold text-sm shadow-lg">
                  BA
                </div>
              </div>
            </div>
          </div>

          {/* Page Content */}
          <main className="p-4 lg:p-8 bg-slate-50 dark:bg-slate-950 min-h-screen">
            {children}
          </main>
        </div>
      </div>
    </div>
  )
}
