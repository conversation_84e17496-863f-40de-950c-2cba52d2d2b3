"use client"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  DollarSign,
  CreditCard,
  Banknote,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Calendar,
  ArrowRight,
  Download,
  Eye,
} from "lucide-react"
import { useState } from "react"

export default function Withdraw() {
  const [withdrawAmount, setWithdrawAmount] = useState("")
  const [selectedMethod, setSelectedMethod] = useState("bank")

  const accountBalance = {
    available: 2925.0,
    pending: 450.0,
    total: 3375.0,
    nextPayout: "2024-01-15",
    minTradingDays: 4,
    currentTradingDays: 8,
  }

  const withdrawalMethods = [
    {
      id: "bank",
      name: "Bank Transfer",
      icon: Banknote,
      fee: "Free",
      time: "1-3 business days",
      min: 50,
      description: "Direct transfer to your bank account",
    },
    {
      id: "paypal",
      name: "PayPal",
      icon: CreditCard,
      fee: "2.5%",
      time: "Instant",
      min: 25,
      description: "Fast transfer to your PayPal account",
    },
    {
      id: "crypto",
      name: "Cryptocurrency",
      icon: DollarSign,
      fee: "1%",
      time: "10-30 minutes",
      min: 100,
      description: "Transfer to your crypto wallet",
    },
  ]

  const recentWithdrawals = [
    {
      id: "WD001",
      amount: 1250.0,
      method: "Bank Transfer",
      status: "completed",
      date: "2024-01-10",
      fee: 0,
    },
    {
      id: "WD002",
      amount: 875.0,
      method: "PayPal",
      status: "processing",
      date: "2024-01-12",
      fee: 21.88,
    },
    {
      id: "WD003",
      amount: 2100.0,
      method: "Cryptocurrency",
      status: "pending",
      date: "2024-01-13",
      fee: 21.0,
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
      case "processing":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
      case "pending":
        return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400"
      default:
        return "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-4 h-4" />
      case "processing":
        return <Clock className="w-4 h-4" />
      case "pending":
        return <AlertCircle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-8 shadow-xl relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10" />
        <div className="relative z-10">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Withdraw Funds</h1>
          <p className="text-lg text-gray-700 dark:text-white/70 mb-6">
            Request a withdrawal from your trading profits
          </p>
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-blue-500" />
              <span className="text-sm text-gray-600 dark:text-white/60">Weekly payouts</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-blue-500" />
              <span className="text-sm text-gray-600 dark:text-white/60">Multiple methods</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-purple-500" />
              <span className="text-sm text-gray-600 dark:text-white/60">Fast processing</span>
            </div>
          </div>
        </div>
      </div>

      {/* Account Balance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-6 shadow-xl">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg">
              <DollarSign className="w-6 h-6 text-white" />
            </div>
            <Badge className="bg-blue-600 dark:bg-blue-500 text-white">Available</Badge>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            ${accountBalance.available.toFixed(2)}
          </div>
          <div className="text-sm text-gray-600 dark:text-white/60">Ready for withdrawal</div>
        </div>

        <div className="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-6 shadow-xl">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg">
              <Clock className="w-6 h-6 text-white" />
            </div>
            <Badge className="bg-blue-600 dark:bg-blue-500 text-white">Pending</Badge>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            ${accountBalance.pending.toFixed(2)}
          </div>
          <div className="text-sm text-gray-600 dark:text-white/60">Processing withdrawals</div>
        </div>

        <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20 backdrop-blur-sm border border-purple-200 dark:border-purple-400/20 rounded-3xl p-6 shadow-xl">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center shadow-lg">
              <TrendingUp className="w-6 h-6 text-white" />
            </div>
            <Badge className="bg-purple-600 dark:bg-purple-500 text-white">Total</Badge>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            ${accountBalance.total.toFixed(2)}
          </div>
          <div className="text-sm text-gray-600 dark:text-white/60">Total balance</div>
        </div>
      </div>

      {/* Withdrawal Form */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">New Withdrawal</h3>

          <div className="space-y-6">
            {/* Amount Input */}
            <div className="space-y-2">
              <Label htmlFor="amount" className="text-sm font-medium text-gray-700 dark:text-white/70">
                Withdrawal Amount
              </Label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                <Input
                  id="amount"
                  type="number"
                  value={withdrawAmount}
                  onChange={(e) => setWithdrawAmount(e.target.value)}
                  className="rounded-xl border-gray-200 dark:border-white/20 bg-white/50 dark:bg-white/5 pl-10"
                  placeholder="0.00"
                  max={accountBalance.available}
                />
              </div>
              <div className="flex justify-between text-sm text-gray-600 dark:text-white/60">
                <span>Available: ${accountBalance.available.toFixed(2)}</span>
                <button
                  onClick={() => setWithdrawAmount(accountBalance.available.toString())}
                  className="text-blue-600 dark:text-blue-400 hover:underline"
                >
                  Max
                </button>
              </div>
            </div>

            {/* Payment Method Selection */}
            <div className="space-y-4">
              <Label className="text-sm font-medium text-gray-700 dark:text-white/70">Payment Method</Label>
              <div className="space-y-3">
                {withdrawalMethods.map((method) => {
                  const IconComponent = method.icon
                  const isSelected = selectedMethod === method.id

                  return (
                    <button
                      key={method.id}
                      onClick={() => setSelectedMethod(method.id)}
                      className={`w-full p-4 rounded-2xl border-2 transition-all duration-300 hover:scale-[1.02] ${
                        isSelected
                          ? "bg-blue-50 dark:bg-blue-500/10 text-blue-700 dark:text-blue-300 border-blue-300 dark:border-blue-400/40 shadow-lg"
                          : "bg-white/50 dark:bg-white/5 text-gray-700 dark:text-white/70 border-gray-200 dark:border-white/20 hover:border-blue-200 dark:hover:border-blue-400/20"
                      }`}
                    >
                      <div className="flex items-center gap-4">
                        <div
                          className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                            isSelected
                              ? "bg-gradient-to-r from-blue-500 to-cyan-500 text-white"
                              : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300"
                          }`}
                        >
                          <IconComponent className="w-5 h-5" />
                        </div>
                        <div className="flex-1 text-left">
                          <div className="font-semibold">{method.name}</div>
                          <div className="text-sm opacity-70">{method.description}</div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium">Fee: {method.fee}</div>
                          <div className="text-xs opacity-70">{method.time}</div>
                        </div>
                      </div>
                    </button>
                  )
                })}
              </div>
            </div>

            {/* Submit Button */}
            <Button
              className="w-full bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600 py-4 text-lg hover:scale-[1.02] transition-all duration-300 hover:shadow-lg"
              disabled={!withdrawAmount || Number.parseFloat(withdrawAmount) <= 0}
            >
              Request Withdrawal
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
          </div>
        </div>

        {/* Withdrawal Info */}
        <div className="space-y-6">
          <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl">
            <h4 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Withdrawal Requirements</h4>
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div
                  className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    accountBalance.currentTradingDays >= accountBalance.minTradingDays
                      ? "bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400"
                      : "bg-gray-100 dark:bg-gray-800 text-gray-400"
                  }`}
                >
                  <CheckCircle className="w-4 h-4" />
                </div>
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">
                    Minimum Trading Days: {accountBalance.currentTradingDays}/{accountBalance.minTradingDays}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-white/60">
                    {accountBalance.currentTradingDays >= accountBalance.minTradingDays
                      ? "Requirement met"
                      : `${accountBalance.minTradingDays - accountBalance.currentTradingDays} more days needed`}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 flex items-center justify-center">
                  <CheckCircle className="w-4 h-4" />
                </div>
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">Account Verification</div>
                  <div className="text-sm text-gray-600 dark:text-white/60">Completed</div>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 flex items-center justify-center">
                  <CheckCircle className="w-4 h-4" />
                </div>
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">Risk Management</div>
                  <div className="text-sm text-gray-600 dark:text-white/60">Within limits</div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-6 shadow-xl">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
                <Calendar className="w-5 h-5 text-white" />
              </div>
              <div>
                <div className="font-bold text-gray-900 dark:text-white">Next Scheduled Payout</div>
                <div className="text-sm text-gray-600 dark:text-white/60">{accountBalance.nextPayout}</div>
              </div>
            </div>
            <div className="text-sm text-gray-600 dark:text-white/60">
              Automatic payouts are processed weekly. You can also request withdrawals anytime after meeting the minimum
              requirements.
            </div>
          </div>
        </div>
      </div>

      {/* Recent Withdrawals */}
      <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Recent Withdrawals</h3>
          <Button variant="outline" className="border-gray-300 dark:border-white/20 bg-transparent">
            <Download className="w-4 h-4 mr-2" />
            Export History
          </Button>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200 dark:border-white/10">
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">ID</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Amount</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Method</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Fee</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Date</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Status</th>
                <th className="text-left py-3 px-4 font-semibold text-gray-700 dark:text-white/70">Action</th>
              </tr>
            </thead>
            <tbody>
              {recentWithdrawals.map((withdrawal) => (
                <tr
                  key={withdrawal.id}
                  className="border-b border-gray-100 dark:border-white/5 hover:bg-gray-50/50 dark:hover:bg-white/5 transition-colors duration-200"
                >
                  <td className="py-4 px-4 font-mono text-sm text-gray-900 dark:text-white">{withdrawal.id}</td>
                  <td className="py-4 px-4 font-bold text-gray-900 dark:text-white">${withdrawal.amount.toFixed(2)}</td>
                  <td className="py-4 px-4 text-gray-700 dark:text-white/70">{withdrawal.method}</td>
                  <td className="py-4 px-4 text-gray-700 dark:text-white/70">${withdrawal.fee.toFixed(2)}</td>
                  <td className="py-4 px-4 text-gray-700 dark:text-white/70">{withdrawal.date}</td>
                  <td className="py-4 px-4">
                    <Badge className={getStatusColor(withdrawal.status)}>
                      {getStatusIcon(withdrawal.status)}
                      <span className="ml-1 capitalize">{withdrawal.status}</span>
                    </Badge>
                  </td>
                  <td className="py-4 px-4">
                    <Button variant="ghost" size="sm">
                      <Eye className="w-4 h-4" />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
