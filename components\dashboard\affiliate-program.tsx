"use client"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  Share2,
  DollarSign,
  TrendingUp,
  Users,
  Target,
  BarChart3,
  Zap,
  Crown,
  Star,
  CheckCircle,
  ArrowRight,
  Download,
  Globe,
} from "lucide-react"

export default function AffiliateProgram() {
  const affiliateStats = {
    totalCommissions: 5420.0,
    monthlyCommissions: 1250.0,
    activeReferrals: 42,
    conversionRate: 18.5,
    tier: "Gold",
    nextTierProgress: 68,
  }

  const commissionTiers = [
    {
      tier: "Bronze",
      referrals: "1-10",
      commission: "15%",
      color: "orange",
      icon: Target,
      perks: ["Basic dashboard", "Monthly payouts", "Email support"],
    },
    {
      tier: "Silver",
      referrals: "11-25",
      commission: "20%",
      color: "gray",
      icon: Star,
      perks: ["Advanced analytics", "Bi-weekly payouts", "Priority support", "Marketing materials"],
    },
    {
      tier: "Gold",
      referrals: "26-50",
      commission: "25%",
      color: "yellow",
      icon: Crown,
      perks: [
        "Real-time tracking",
        "Weekly payouts",
        "Dedicated manager",
        "Custom landing pages",
        "Exclusive webinars",
      ],
      current: true,
    },
    {
      tier: "Platinum",
      referrals: "51+",
      commission: "30%",
      color: "purple",
      icon: Zap,
      perks: [
        "Premium dashboard",
        "Daily payouts",
        "VIP support",
        "Co-marketing opportunities",
        "Revenue sharing",
        "Personal branding",
      ],
    },
  ]

  const marketingMaterials = [
    {
      title: "Banner Ads",
      description: "High-converting display banners in multiple sizes",
      icon: BarChart3,
      count: "12 designs",
      color: "blue",
    },
    {
      title: "Landing Pages",
      description: "Optimized conversion pages with your referral link",
      icon: Globe,
      count: "5 templates",
      color: "green",
    },
    {
      title: "Email Templates",
      description: "Professional email campaigns for your audience",
      icon: Users,
      count: "8 templates",
      color: "purple",
    },
    {
      title: "Social Media Kit",
      description: "Ready-to-post content for all major platforms",
      icon: Share2,
      count: "20+ posts",
      color: "pink",
    },
  ]

  const recentPayouts = [
    {
      date: "2024-01-15",
      amount: 1250.0,
      referrals: 8,
      status: "paid",
    },
    {
      date: "2024-01-08",
      amount: 875.5,
      referrals: 6,
      status: "paid",
    },
    {
      date: "2024-01-01",
      amount: 1420.75,
      referrals: 12,
      status: "paid",
    },
  ]

  const getColorClasses = (color: string, current = false) => {
    const colorMap = {
      orange: {
        bg: "from-orange-500/10 to-amber-500/10 dark:from-orange-500/20 dark:to-amber-500/20",
        border: current
          ? "border-orange-400/50 dark:border-orange-400/40"
          : "border-orange-200 dark:border-orange-400/20",
        icon: "text-orange-600 dark:text-orange-400",
        iconBg: "from-orange-500 to-amber-500",
      },
      gray: {
        bg: "from-gray-500/10 to-slate-500/10 dark:from-gray-500/20 dark:to-slate-500/20",
        border: current ? "border-gray-400/50 dark:border-gray-400/40" : "border-gray-200 dark:border-gray-400/20",
        icon: "text-gray-600 dark:text-gray-400",
        iconBg: "from-gray-500 to-slate-500",
      },
      yellow: {
        bg: "from-yellow-500/10 to-amber-500/10 dark:from-yellow-500/20 dark:to-amber-500/20",
        border: current
          ? "border-yellow-400/50 dark:border-yellow-400/40"
          : "border-yellow-200 dark:border-yellow-400/20",
        icon: "text-yellow-600 dark:text-yellow-400",
        iconBg: "from-yellow-500 to-amber-500",
      },
      purple: {
        bg: "from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20",
        border: current
          ? "border-purple-400/50 dark:border-purple-400/40"
          : "border-purple-200 dark:border-purple-400/20",
        icon: "text-purple-600 dark:text-purple-400",
        iconBg: "from-purple-500 to-pink-500",
      },
      blue: {
        bg: "from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20",
        border: "border-blue-200 dark:border-blue-400/20",
        icon: "text-blue-600 dark:text-blue-400",
        iconBg: "from-blue-500 to-cyan-500",
      },
      green: {
        bg: "from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20",
        border: "border-green-200 dark:border-green-400/20",
        icon: "text-green-600 dark:text-green-400",
        iconBg: "from-green-500 to-emerald-500",
      },
      pink: {
        bg: "from-pink-500/10 to-rose-500/10 dark:from-pink-500/20 dark:to-rose-500/20",
        border: "border-pink-200 dark:border-pink-400/20",
        icon: "text-pink-600 dark:text-pink-400",
        iconBg: "from-pink-500 to-rose-500",
      },
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.orange
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20 backdrop-blur-sm border border-purple-200 dark:border-purple-400/20 rounded-3xl p-8 shadow-xl relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 dark:from-purple-500/10 dark:to-pink-500/10" />
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Affiliate Program</h1>
              <p className="text-lg text-gray-700 dark:text-white/70">
                Earn up to 30% commission by promoting Apex Capital
              </p>
            </div>
            <Badge className="bg-yellow-600 dark:bg-yellow-500 text-white text-lg px-4 py-2">
              <Crown className="w-5 h-5 mr-2" />
              {affiliateStats.tier} Tier
            </Badge>
          </div>
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-purple-500" />
              <span className="text-sm text-gray-600 dark:text-white/60">Premium marketing tools</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-green-500" />
              <span className="text-sm text-gray-600 dark:text-white/60">Weekly payouts</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-blue-500" />
              <span className="text-sm text-gray-600 dark:text-white/60">Dedicated support</span>
            </div>
          </div>
        </div>
      </div>

      {/* Affiliate Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20 backdrop-blur-sm border border-green-200 dark:border-green-400/20 rounded-3xl p-6 shadow-xl">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center shadow-lg">
              <DollarSign className="w-6 h-6 text-white" />
            </div>
            <Badge className="bg-green-600 dark:bg-green-500 text-white">Total</Badge>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            ${affiliateStats.totalCommissions.toFixed(2)}
          </div>
          <div className="text-sm text-gray-600 dark:text-white/60">Total Commissions</div>
        </div>

        <div className="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-6 shadow-xl">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg">
              <TrendingUp className="w-6 h-6 text-white" />
            </div>
            <Badge className="bg-blue-600 dark:bg-blue-500 text-white">This Month</Badge>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            ${affiliateStats.monthlyCommissions.toFixed(2)}
          </div>
          <div className="text-sm text-gray-600 dark:text-white/60">Monthly Earnings</div>
        </div>

        <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20 backdrop-blur-sm border border-purple-200 dark:border-purple-400/20 rounded-3xl p-6 shadow-xl">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center shadow-lg">
              <Users className="w-6 h-6 text-white" />
            </div>
            <Badge className="bg-purple-600 dark:bg-purple-500 text-white">Active</Badge>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">{affiliateStats.activeReferrals}</div>
          <div className="text-sm text-gray-600 dark:text-white/60">Active Referrals</div>
        </div>

        <div className="bg-gradient-to-br from-orange-500/10 to-amber-500/10 dark:from-orange-500/20 dark:to-amber-500/20 backdrop-blur-sm border border-orange-200 dark:border-orange-400/20 rounded-3xl p-6 shadow-xl">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-orange-500 to-amber-500 flex items-center justify-center shadow-lg">
              <Target className="w-6 h-6 text-white" />
            </div>
            <Badge className="bg-orange-600 dark:bg-orange-500 text-white">Excellent</Badge>
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">{affiliateStats.conversionRate}%</div>
          <div className="text-sm text-gray-600 dark:text-white/60">Conversion Rate</div>
        </div>
      </div>

      {/* Commission Tiers */}
      <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Commission Tiers</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {commissionTiers.map((tier, index) => {
            const colors = getColorClasses(tier.color, tier.current)
            const IconComponent = tier.icon

            return (
              <div key={index} className="relative group">
                {tier.current && (
                  <div className="absolute -top-4 left-1/2 -translate-x-1/2 z-20">
                    <Badge className="bg-green-600 dark:bg-green-500 text-white text-xs px-3 py-1 shadow-lg">
                      <Star className="w-3 h-3 mr-1" />
                      CURRENT
                    </Badge>
                  </div>
                )}

                <div
                  className={`bg-gradient-to-br ${colors.bg} backdrop-blur-sm border-2 ${colors.border} rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] relative overflow-hidden h-full ${tier.current ? "ring-2 ring-green-400/20 dark:ring-green-400/30" : ""}`}
                >
                  <div className="text-center mb-6">
                    <div
                      className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${colors.iconBg} flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-110 transition-transform duration-300`}
                    >
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">{tier.tier}</h4>
                    <p className="text-sm text-gray-600 dark:text-white/60 mb-4">{tier.referrals} referrals</p>
                    <div className={`text-4xl font-bold ${colors.icon} mb-2`}>{tier.commission}</div>
                    <p className="text-sm text-gray-600 dark:text-white/60">Commission Rate</p>
                  </div>

                  <div>
                    <h5 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Perks Include:</h5>
                    <ul className="space-y-3">
                      {tier.perks.map((perk, perkIndex) => (
                        <li key={perkIndex} className="flex items-start gap-3">
                          <div className="w-2 h-2 rounded-full bg-gradient-to-r from-green-500 to-emerald-500 mt-2 flex-shrink-0" />
                          <span className="text-sm text-gray-700 dark:text-white/70">{perk}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Next Tier Progress */}
        <div className="p-6 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 dark:from-yellow-500/20 dark:to-orange-500/20 rounded-2xl border border-yellow-200 dark:border-yellow-400/20">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-yellow-500 to-orange-500 flex items-center justify-center">
              <TrendingUp className="w-4 h-4 text-white" />
            </div>
            <div className="font-bold text-gray-900 dark:text-white">Progress to Platinum Tier</div>
          </div>
          <div className="text-sm text-gray-600 dark:text-white/60 mb-3">
            You need {51 - affiliateStats.activeReferrals} more active referrals to reach Platinum tier (30% commission)
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
            <div
              className="bg-gradient-to-r from-yellow-500 to-orange-500 h-3 rounded-full transition-all duration-500"
              style={{ width: `${affiliateStats.nextTierProgress}%` }}
            />
          </div>
          <div className="text-right text-sm text-gray-600 dark:text-white/60 mt-1">
            {affiliateStats.nextTierProgress}% Complete
          </div>
        </div>
      </div>

      {/* Marketing Materials */}
      <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Marketing Materials</h3>
          <Button className="bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-500 dark:to-emerald-500 text-white hover:from-green-700 hover:to-emerald-700 dark:hover:from-green-600 dark:hover:to-emerald-600">
            <Download className="w-4 h-4 mr-2" />
            Download All
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {marketingMaterials.map((material, index) => {
            const colors = getColorClasses(material.color)
            const IconComponent = material.icon

            return (
              <div
                key={index}
                className={`bg-gradient-to-br ${colors.bg} backdrop-blur-sm border ${colors.border} rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] group`}
              >
                <div className="flex items-center gap-4 mb-4">
                  <div
                    className={`w-12 h-12 rounded-2xl bg-gradient-to-r ${colors.iconBg} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}
                  >
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-gray-900 dark:text-white">{material.title}</h4>
                    <Badge className={`${colors.icon} bg-transparent border ${colors.border} text-xs`}>
                      {material.count}
                    </Badge>
                  </div>
                </div>
                <p className="text-gray-700 dark:text-white/70 mb-4">{material.description}</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full border-gray-300 dark:border-white/20 text-gray-700 dark:text-white/70 hover:bg-gray-50 dark:hover:bg-white/5 bg-transparent"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download
                </Button>
              </div>
            )
          })}
        </div>
      </div>

      {/* Recent Payouts */}
      <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Recent Payouts</h3>

        <div className="space-y-4">
          {recentPayouts.map((payout, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-4 bg-gray-50 dark:bg-white/5 rounded-2xl hover:bg-gray-100 dark:hover:bg-white/10 transition-colors duration-200"
            >
              <div className="flex items-center gap-4">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center">
                  <CheckCircle className="w-5 h-5 text-white" />
                </div>
                <div>
                  <div className="font-bold text-gray-900 dark:text-white">${payout.amount.toFixed(2)}</div>
                  <div className="text-sm text-gray-600 dark:text-white/60">
                    {payout.referrals} referrals • {payout.date}
                  </div>
                </div>
              </div>
              <Badge className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400">
                <CheckCircle className="w-4 h-4 mr-1" />
                Paid
              </Badge>
            </div>
          ))}
        </div>
      </div>

      {/* CTA */}
      <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20 backdrop-blur-sm border border-purple-200 dark:border-purple-400/20 rounded-3xl p-8 shadow-xl">
        <div className="text-center">
          <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">Ready to Maximize Your Earnings?</h3>
          <p className="text-lg text-gray-700 dark:text-white/70 mb-8 max-w-2xl mx-auto">
            Join our exclusive affiliate program and start earning substantial commissions with premium marketing tools
            and dedicated support.
          </p>
          <Button className="rounded-full bg-gradient-to-r from-purple-600 to-pink-600 dark:from-purple-500 dark:to-pink-500 text-white hover:from-purple-700 hover:to-pink-700 dark:hover:from-purple-600 dark:hover:to-pink-600 px-8 py-4 text-lg hover:scale-105 transition-all duration-300 hover:shadow-lg">
            Upgrade to Premium
            <ArrowRight className="ml-2 h-5 w-5" />
          </Button>
        </div>
      </div>
    </div>
  )
}
