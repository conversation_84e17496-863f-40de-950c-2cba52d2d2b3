"use client"

import type React from "react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import {
  User,
  Mail,
  Lock,
  Eye,
  EyeOff,
  Phone,
  Calendar,
  TrendingUp,
  Shield,
  Zap,
  ArrowLeft,
  Building,
  CreditCard,
  Globe,
  CheckCircle,
  AlertCircle,
  Check,
  Star,
  Award,
  Users,
  BarChart3,
} from "lucide-react"
import { useState, useEffect } from "react"
import Link from "next/link"
import { useSearchParams, useRouter } from "next/navigation"
import { useLanguage } from "@/contexts/language-context"
import LanguageSelector from "@/components/language-selector"

export default function AuthPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [mode, setMode] = useState<"login" | "signup">("login")
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { t, isRTL } = useLanguage()
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    country: "",
    dateOfBirth: "",
    address: "",
    city: "",
    postalCode: "",
    tradingExperience: "",
    password: "",
    confirmPassword: "",
    agreeToTerms: false,
    agreeToMarketing: false,
    agreeToRisk: false,
  })

  useEffect(() => {
    const modeParam = searchParams.get("mode")
    if (modeParam === "signup" || modeParam === "login") {
      setMode(modeParam)
    }
  }, [searchParams])

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000))

    if (mode === "login") {
      // Redirect to dashboard after successful login
      router.push("/dashboard")
    } else {
      // Show success message or redirect to verification
      console.log("Account created successfully")
      router.push("/dashboard")
    }

    setIsLoading(false)
  }

  const switchMode = () => {
    const newMode = mode === "login" ? "signup" : "login"
    setMode(newMode)
    router.push(`/auth?mode=${newMode}`)
    setFormData({
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      country: "",
      dateOfBirth: "",
      address: "",
      city: "",
      postalCode: "",
      tradingExperience: "",
      password: "",
      confirmPassword: "",
      agreeToTerms: false,
      agreeToMarketing: false,
      agreeToRisk: false,
    })
  }

  const countries = [
    "United States",
    "United Kingdom",
    "Canada",
    "Australia",
    "Germany",
    "France",
    "Italy",
    "Spain",
    "Netherlands",
    "Sweden",
    "Norway",
    "Denmark",
  ]

  const experienceLevels = [
    "Beginner (0-1 years)",
    "Intermediate (1-3 years)",
    "Advanced (3-5 years)",
    "Expert (5+ years)",
  ]

  const features = [
    {
      icon: <TrendingUp className="w-5 h-5" />,
      title: "Up to $400K",
      subtitle: "Funding",
      color: "from-blue-500 to-blue-600",
    },
    {
      icon: <Shield className="w-5 h-5" />,
      title: "90% Split",
      subtitle: "Profit Share",
      color: "from-emerald-500 to-emerald-600",
    },
    {
      icon: <Zap className="w-5 h-5" />,
      title: "24-48h",
      subtitle: "Fast Funding",
      color: "from-purple-500 to-purple-600",
    },
  ]

  const testimonials = [
    {
      name: "Sarah Johnson",
      role: "Professional Trader",
      content: "Apex Capital transformed my trading career. The funding process was seamless and the support is exceptional.",
      rating: 5,
    },
    {
      name: "Michael Chen",
      role: "Funded Trader",
      content: "Best prop trading firm I've worked with. Transparent rules and fast payouts.",
      rating: 5,
    },
  ]

  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 ${isRTL ? "rtl" : "ltr"}`}>
      {/* Animated Background */}
      <div className="fixed inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full blur-3xl animate-pulse" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-emerald-500/10 to-teal-500/10 rounded-full blur-3xl animate-pulse delay-500" />
      </div>

      <div className="relative z-10 min-h-screen">
        {/* Header */}
        <div className="flex items-center justify-between p-6 lg:p-8">
            <Link href="/">
              <Button
                variant="ghost"
              className="text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-white transition-colors"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                {t("auth.backToHome")}
              </Button>
            </Link>
            <LanguageSelector />
          </div>

        <div className="flex items-center justify-center px-4 py-8 lg:py-12">
          <div className="w-full max-w-7xl grid lg:grid-cols-2 gap-8 lg:gap-12">
            {/* Left Side - Form */}
            <div className="flex items-center justify-center">
              <div className="w-full max-w-md">
          {/* Auth Card */}
                <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl border border-slate-200/50 dark:border-slate-700/50 rounded-3xl shadow-2xl p-8">
            {/* Header */}
                  <div className="text-center mb-8">
                    <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-blue-600 to-cyan-600 flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <TrendingUp className="w-8 h-8 text-white" />
                </div>
                    <h1 className="text-3xl font-bold text-slate-900 dark:text-white mb-2">
                    {mode === "login" ? t("auth.welcomeBack") : t("auth.joinApex")}
                  </h1>
                    <p className="text-slate-600 dark:text-slate-400 text-lg">
                    {mode === "login" ? t("auth.signInAccess") : t("auth.startJourney")}
                  </p>
            </div>

            {/* Form */}
              <form onSubmit={handleSubmit} className="space-y-6">
                {mode === "signup" && (
                  <>
                    {/* Personal Information */}
                    <div className="space-y-4">
                          <div className="flex items-center gap-2 mb-4">
                            <div className="w-8 h-8 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                              <User className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                            </div>
                            <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                        {t("auth.personalInfo")}
                      </h3>
                          </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                              <Label htmlFor="firstName" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                {t("auth.firstName")} <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="firstName"
                            type="text"
                            value={formData.firstName}
                            onChange={(e) => handleInputChange("firstName", e.target.value)}
                                className="rounded-xl border-slate-200 dark:border-slate-700 bg-white/50 dark:bg-slate-800/50 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="John"
                            required
                          />
                        </div>
                        <div className="space-y-2">
                              <Label htmlFor="lastName" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                {t("auth.lastName")} <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="lastName"
                            type="text"
                            value={formData.lastName}
                            onChange={(e) => handleInputChange("lastName", e.target.value)}
                                className="rounded-xl border-slate-200 dark:border-slate-700 bg-white/50 dark:bg-slate-800/50 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Doe"
                            required
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                            <Label htmlFor="dateOfBirth" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                              {t("auth.dateOfBirth")} <span className="text-red-500">*</span>
                        </Label>
                        <div className="relative">
                              <Calendar className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                          <Input
                            id="dateOfBirth"
                            type="date"
                            value={formData.dateOfBirth}
                            onChange={(e) => handleInputChange("dateOfBirth", e.target.value)}
                                className="rounded-xl border-slate-200 dark:border-slate-700 bg-white/50 dark:bg-slate-800/50 pl-10 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            required
                          />
                        </div>
                      </div>
                    </div>

                    {/* Contact Information */}
                    <div className="space-y-4">
                          <div className="flex items-center gap-2 mb-4">
                            <div className="w-8 h-8 rounded-lg bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center">
                              <Mail className="w-4 h-4 text-emerald-600 dark:text-emerald-400" />
                            </div>
                            <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                        {t("auth.contactInfo")}
                      </h3>
                          </div>

                      <div className="space-y-2">
                            <Label htmlFor="phone" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                              {t("auth.phoneNumber")} <span className="text-red-500">*</span>
                        </Label>
                        <div className="relative">
                              <Phone className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                          <Input
                            id="phone"
                            type="tel"
                            value={formData.phone}
                            onChange={(e) => handleInputChange("phone", e.target.value)}
                                className="rounded-xl border-slate-200 dark:border-slate-700 bg-white/50 dark:bg-slate-800/50 pl-10 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="+****************"
                            required
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                            <Label htmlFor="address" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                              {t("auth.address")} <span className="text-red-500">*</span>
                        </Label>
                        <div className="relative">
                              <Building className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                          <Input
                            id="address"
                            type="text"
                            value={formData.address}
                            onChange={(e) => handleInputChange("address", e.target.value)}
                                className="rounded-xl border-slate-200 dark:border-slate-700 bg-white/50 dark:bg-slate-800/50 pl-10 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="123 Main Street"
                            required
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                              <Label htmlFor="city" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                {t("auth.city")} <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="city"
                            type="text"
                            value={formData.city}
                            onChange={(e) => handleInputChange("city", e.target.value)}
                                className="rounded-xl border-slate-200 dark:border-slate-700 bg-white/50 dark:bg-slate-800/50 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="New York"
                            required
                          />
                        </div>
                        <div className="space-y-2">
                              <Label htmlFor="postalCode" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                {t("auth.postalCode")} <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="postalCode"
                            type="text"
                            value={formData.postalCode}
                            onChange={(e) => handleInputChange("postalCode", e.target.value)}
                                className="rounded-xl border-slate-200 dark:border-slate-700 bg-white/50 dark:bg-slate-800/50 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="10001"
                            required
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                            <Label htmlFor="country" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                              {t("auth.country")} <span className="text-red-500">*</span>
                        </Label>
                        <div className="relative">
                              <Globe className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                          <select
                            id="country"
                            value={formData.country}
                            onChange={(e) => handleInputChange("country", e.target.value)}
                                className="w-full rounded-xl border border-slate-200 dark:border-slate-700 bg-white/50 dark:bg-slate-800/50 px-10 py-3 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            required
                          >
                            <option value="">{t("auth.selectCountry")}</option>
                            {countries.map((country) => (
                                  <option key={country} value={country} className="bg-white dark:bg-slate-800">
                                {country}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                    </div>

                    {/* Trading Experience */}
                    <div className="space-y-4">
                          <div className="flex items-center gap-2 mb-4">
                            <div className="w-8 h-8 rounded-lg bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center">
                              <BarChart3 className="w-4 h-4 text-purple-600 dark:text-purple-400" />
                            </div>
                            <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                        {t("auth.tradingExp")}
                      </h3>
                          </div>

                      <div className="space-y-2">
                        <Label
                          htmlFor="tradingExperience"
                              className="text-sm font-medium text-slate-700 dark:text-slate-300"
                        >
                              {t("auth.experienceLevel")} <span className="text-red-500">*</span>
                        </Label>
                        <div className="relative">
                              <CreditCard className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                          <select
                            id="tradingExperience"
                            value={formData.tradingExperience}
                            onChange={(e) => handleInputChange("tradingExperience", e.target.value)}
                                className="w-full rounded-xl border border-slate-200 dark:border-slate-700 bg-white/50 dark:bg-slate-800/50 px-10 py-3 text-slate-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            required
                          >
                            <option value="">{t("auth.selectExperience")}</option>
                            {experienceLevels.map((level) => (
                                  <option key={level} value={level} className="bg-white dark:bg-slate-800">
                                {level}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {/* Email and Password */}
                <div className="space-y-4">
                      <div className="flex items-center gap-2 mb-4">
                        <div className="w-8 h-8 rounded-lg bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center">
                          <Lock className="w-4 h-4 text-orange-600 dark:text-orange-400" />
                        </div>
                        <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                    {t("auth.accountCreds")}
                  </h3>
                      </div>

                  <div className="space-y-2">
                        <Label htmlFor="email" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                          {t("auth.emailAddress")} <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                          <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange("email", e.target.value)}
                            className="rounded-xl border-slate-200 dark:border-slate-700 bg-white/50 dark:bg-slate-800/50 pl-10 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                        <Label htmlFor="password" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                          {t("auth.password")} <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                          <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                      <Input
                        id="password"
                        type={showPassword ? "text" : "password"}
                        value={formData.password}
                        onChange={(e) => handleInputChange("password", e.target.value)}
                            className="rounded-xl border-slate-200 dark:border-slate-700 bg-white/50 dark:bg-slate-800/50 pl-10 pr-10 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="••••••••"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
                      >
                        {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </button>
                    </div>
                    {mode === "signup" && (
                          <div className="flex items-center gap-2 text-xs text-slate-600 dark:text-slate-400 mt-1">
                            <AlertCircle className="w-3 h-3" />
                        {t("auth.passwordRequirement")}
                      </div>
                    )}
                  </div>

                  {mode === "signup" && (
                    <div className="space-y-2">
                          <Label htmlFor="confirmPassword" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                            {t("auth.confirmPassword")} <span className="text-red-500">*</span>
                      </Label>
                      <div className="relative">
                            <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-slate-400" />
                        <Input
                          id="confirmPassword"
                          type={showConfirmPassword ? "text" : "password"}
                          value={formData.confirmPassword}
                          onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
                              className="rounded-xl border-slate-200 dark:border-slate-700 bg-white/50 dark:bg-slate-800/50 pl-10 pr-10 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="••••••••"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                              className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors"
                        >
                          {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                {mode === "signup" && (
                  <div className="space-y-4">
                        <div className="flex items-center gap-2 mb-4">
                          <div className="w-8 h-8 rounded-lg bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                            <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
                          </div>
                          <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
                      {t("auth.agreements")}
                    </h3>
                        </div>

                    <div className="space-y-4">
                      <div className="flex items-start space-x-3">
                        <Checkbox
                          id="agreeToTerms"
                          checked={formData.agreeToTerms}
                          onCheckedChange={(checked) => handleInputChange("agreeToTerms", checked as boolean)}
                          className="mt-1"
                        />
                        <Label
                          htmlFor="agreeToTerms"
                              className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed"
                        >
                          {t("auth.agreeTerms")}
                        </Label>
                      </div>

                      <div className="flex items-start space-x-3">
                        <Checkbox
                          id="agreeToRisk"
                          checked={formData.agreeToRisk}
                          onCheckedChange={(checked) => handleInputChange("agreeToRisk", checked as boolean)}
                          className="mt-1"
                        />
                        <Label
                          htmlFor="agreeToRisk"
                              className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed"
                        >
                          {t("auth.agreeRisk")}
                        </Label>
                      </div>

                      <div className="flex items-start space-x-3">
                        <Checkbox
                          id="agreeToMarketing"
                          checked={formData.agreeToMarketing}
                          onCheckedChange={(checked) => handleInputChange("agreeToMarketing", checked as boolean)}
                          className="mt-1"
                        />
                        <Label
                          htmlFor="agreeToMarketing"
                              className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed"
                        >
                          {t("auth.agreeMarketing")}
                        </Label>
                      </div>
                    </div>
                  </div>
                )}

                <Button
                  type="submit"
                  disabled={isLoading}
                      className="w-full rounded-xl bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600 py-4 text-lg font-semibold hover:scale-[1.02] transition-all duration-300 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      {mode === "login" ? t("auth.signingIn") : t("auth.creatingAccount")}
                    </div>
                  ) : mode === "login" ? (
                    t("auth.signIn")
                  ) : (
                    t("auth.createAccount")
                  )}
                </Button>

                {mode === "login" && (
                  <div className="text-center">
                        <a href="#" className="text-sm text-blue-600 dark:text-blue-400 hover:underline font-medium">
                      {t("auth.forgotPassword")}
                    </a>
                  </div>
                )}

                <div className="text-center">
                      <span className="text-sm text-slate-600 dark:text-slate-400">
                    {mode === "login" ? t("auth.noAccount") : t("auth.haveAccount")}{" "}
                    <button
                      type="button"
                      onClick={switchMode}
                          className="text-blue-600 dark:text-blue-400 hover:underline font-semibold"
                    >
                      {mode === "login" ? t("auth.signUp") : t("auth.signIn")}
                    </button>
                  </span>
                </div>
              </form>
                </div>
              </div>
            </div>

            {/* Right Side - Features & Testimonials */}
            <div className="hidden lg:flex flex-col justify-center space-y-8">
              {/* Features */}
              <div className="space-y-6">
                <div className="text-center">
                  <h2 className="text-3xl font-bold text-slate-900 dark:text-white mb-4">
                    Why Choose Apex Capital?
                  </h2>
                  <p className="text-slate-600 dark:text-slate-400 text-lg">
                    Join thousands of successful traders who trust us with their trading career
                  </p>
                </div>

                <div className="grid grid-cols-1 gap-4">
                  {features.map((feature, index) => (
                    <div
                      key={index}
                      className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6 hover:shadow-lg transition-all duration-300 hover:scale-105"
                    >
                      <div className="flex items-center gap-4">
                        <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${feature.color} flex items-center justify-center shadow-lg`}>
                          <div className="text-white">{feature.icon}</div>
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-slate-900 dark:text-white">{feature.title}</h3>
                          <p className="text-slate-600 dark:text-slate-400">{feature.subtitle}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Testimonials */}
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-2xl font-bold text-slate-900 dark:text-white mb-4">
                    What Our Traders Say
                  </h3>
                </div>

                <div className="space-y-4">
                  {testimonials.map((testimonial, index) => (
                    <div
                      key={index}
                      className="bg-white/60 dark:bg-slate-800/60 backdrop-blur-sm border border-slate-200/50 dark:border-slate-700/50 rounded-2xl p-6"
                    >
                      <div className="flex items-start gap-4">
                        <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center text-white font-semibold">
                          {testimonial.name.charAt(0)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <div className="flex items-center gap-1">
                              {[...Array(testimonial.rating)].map((_, i) => (
                                <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                              ))}
                            </div>
                          </div>
                          <p className="text-slate-700 dark:text-slate-300 mb-3 italic">
                            "{testimonial.content}"
                          </p>
                          <div>
                            <p className="font-semibold text-slate-900 dark:text-white">{testimonial.name}</p>
                            <p className="text-sm text-slate-600 dark:text-slate-400">{testimonial.role}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Stats */}
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-white/40 dark:bg-slate-800/40 rounded-xl">
                    <div className="text-2xl font-bold text-slate-900 dark:text-white">$50M+</div>
                    <div className="text-sm text-slate-600 dark:text-slate-400">Paid to Traders</div>
                  </div>
                  <div className="text-center p-4 bg-white/40 dark:bg-slate-800/40 rounded-xl">
                    <div className="text-2xl font-bold text-slate-900 dark:text-white">10K+</div>
                    <div className="text-sm text-slate-600 dark:text-slate-400">Active Traders</div>
                  </div>
                  <div className="text-center p-4 bg-white/40 dark:bg-slate-800/40 rounded-xl">
                    <div className="text-2xl font-bold text-slate-900 dark:text-white">98%</div>
                    <div className="text-sm text-slate-600 dark:text-slate-400">Success Rate</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
