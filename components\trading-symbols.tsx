"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { TrendingUp, TrendingDown, BarChart3, Globe, Search, Filter } from "lucide-react"
import { Input } from "@/components/ui/input"
import { useState } from "react"

export default function TradingSymbols() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")

  const tradingSymbols = [
    {
      symbol: "EURUSD",
      name: "Euro vs US Dollar",
      category: "major",
      price: "1.0856",
      change: "+0.0023",
      changePercent: "+0.21%",
      trend: "up",
      spread: "0.8",
      volume: "High",
      flag1: "🇪🇺",
      flag2: "🇺🇸",
    },
    {
      symbol: "GBPUSD",
      name: "British Pound vs US Dollar",
      category: "major",
      price: "1.2734",
      change: "-0.0045",
      changePercent: "-0.35%",
      trend: "down",
      spread: "1.2",
      volume: "High",
      flag1: "🇬🇧",
      flag2: "🇺🇸",
    },
    {
      symbol: "USDJPY",
      name: "US Dollar vs Japanese Yen",
      category: "major",
      price: "149.85",
      change: "+0.67",
      changePercent: "+0.45%",
      trend: "up",
      spread: "1.0",
      volume: "High",
      flag1: "🇺🇸",
      flag2: "🇯🇵",
    },
    {
      symbol: "USDCAD",
      name: "US Dollar vs Canadian Dollar",
      category: "major",
      price: "1.3567",
      change: "-0.0012",
      changePercent: "-0.09%",
      trend: "down",
      spread: "1.5",
      volume: "Medium",
      flag1: "🇺🇸",
      flag2: "🇨🇦",
    },
    {
      symbol: "AUDUSD",
      name: "Australian Dollar vs US Dollar",
      category: "major",
      price: "0.6523",
      change: "+0.0034",
      changePercent: "+0.52%",
      trend: "up",
      spread: "1.3",
      volume: "Medium",
      flag1: "🇦🇺",
      flag2: "🇺🇸",
    },
    {
      symbol: "USDCHF",
      name: "US Dollar vs Swiss Franc",
      category: "major",
      price: "0.8945",
      change: "+0.0018",
      changePercent: "+0.20%",
      trend: "up",
      spread: "1.4",
      volume: "Medium",
      flag1: "🇺🇸",
      flag2: "🇨🇭",
    },
    {
      symbol: "EURGBP",
      name: "Euro vs British Pound",
      category: "cross",
      price: "0.8523",
      change: "+0.0015",
      changePercent: "+0.18%",
      trend: "up",
      spread: "1.8",
      volume: "Medium",
      flag1: "🇪🇺",
      flag2: "🇬🇧",
    },
    {
      symbol: "EURJPY",
      name: "Euro vs Japanese Yen",
      category: "cross",
      price: "162.74",
      change: "+0.89",
      changePercent: "+0.55%",
      trend: "up",
      spread: "2.1",
      volume: "Medium",
      flag1: "🇪🇺",
      flag2: "🇯🇵",
    },
    {
      symbol: "GBPJPY",
      name: "British Pound vs Japanese Yen",
      category: "cross",
      price: "190.92",
      change: "-0.45",
      changePercent: "-0.24%",
      trend: "down",
      spread: "2.5",
      volume: "Medium",
      flag1: "🇬🇧",
      flag2: "🇯🇵",
    },
    {
      symbol: "NZDUSD",
      name: "New Zealand Dollar vs US Dollar",
      category: "minor",
      price: "0.6123",
      change: "+0.0028",
      changePercent: "+0.46%",
      trend: "up",
      spread: "2.0",
      volume: "Low",
      flag1: "🇳🇿",
      flag2: "🇺🇸",
    },
    {
      symbol: "USDSEK",
      name: "US Dollar vs Swedish Krona",
      category: "minor",
      price: "10.4567",
      change: "-0.0234",
      changePercent: "-0.22%",
      trend: "down",
      spread: "3.2",
      volume: "Low",
      flag1: "🇺🇸",
      flag2: "🇸🇪",
    },
    {
      symbol: "USDNOK",
      name: "US Dollar vs Norwegian Krone",
      category: "minor",
      price: "10.8934",
      change: "+0.0456",
      changePercent: "+0.42%",
      trend: "up",
      spread: "3.8",
      volume: "Low",
      flag1: "🇺🇸",
      flag2: "🇳🇴",
    },
  ]

  const categories = [
    { value: "all", label: "All Pairs", count: tradingSymbols.length },
    { value: "major", label: "Major Pairs", count: tradingSymbols.filter((s) => s.category === "major").length },
    { value: "cross", label: "Cross Pairs", count: tradingSymbols.filter((s) => s.category === "cross").length },
    { value: "minor", label: "Minor Pairs", count: tradingSymbols.filter((s) => s.category === "minor").length },
  ]

  const filteredSymbols = tradingSymbols.filter((symbol) => {
    const matchesSearch =
      symbol.symbol.toLowerCase().includes(searchQuery.toLowerCase()) ||
      symbol.name.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === "all" || symbol.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const getVolumeColor = (volume: string) => {
    switch (volume) {
      case "High":
        return "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
      case "Medium":
        return "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400"
      case "Low":
        return "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
      default:
        return "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300"
    }
  }

  return (
    <section className="py-24 md:py-32 relative" aria-labelledby="symbols-heading">
      <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
        {/* Section Header */}
        <div className="mb-20 md:mb-28 max-w-4xl mx-auto text-center">
          <Badge
            variant="outline"
            className="mb-8 md:mb-12 text-sm font-light border-gray-300 dark:border-white/20 text-gray-600 dark:text-white/80 px-4 py-2 items-center"
          >
            <BarChart3 className="w-4 h-4 mr-2" />
            Trading Symbols
          </Badge>
          <h2 id="symbols-heading" className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 md:mb-12 leading-tight">
            Trade{" "}
            <span className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400 bg-clip-text text-transparent">
              global markets
            </span>
          </h2>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 leading-relaxed">
            Access over 50+ currency pairs with competitive spreads and lightning-fast execution. Trade major, minor,
            and exotic pairs 24/5.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-12">
          <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 shadow-xl">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
                <Filter className="w-5 h-5 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">Find Trading Pairs</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Search */}
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-white/70 mb-3 block">
                  Search Symbols
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <Input
                    type="text"
                    placeholder="Search currency pairs..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="rounded-xl border-gray-200 dark:border-white/20 bg-white/50 dark:bg-white/5 pl-10"
                  />
                </div>
              </div>

              {/* Category Filter */}
              <div>
                <label className="text-sm font-medium text-gray-700 dark:text-white/70 mb-3 block">Category</label>
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <button
                      key={category.value}
                      onClick={() => setSelectedCategory(category.value)}
                      className={`px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 hover:scale-105 ${
                        selectedCategory === category.value
                          ? "bg-blue-600 dark:bg-blue-500 text-white shadow-lg"
                          : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
                      }`}
                    >
                      {category.label} ({category.count})
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Trading Symbols Table */}
        <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 shadow-xl">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center shadow-lg">
                <Globe className="w-6 h-6 text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Live Prices</h3>
                <p className="text-sm text-gray-600 dark:text-white/60">
                  {filteredSymbols.length} pairs • Real-time quotes
                </p>
              </div>
            </div>
            <Button className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600">
              <BarChart3 className="w-4 h-4 mr-2" />
              Advanced Charts
            </Button>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-white/10">
                  <th className="text-left py-4 px-4 font-semibold text-gray-700 dark:text-white/70">Symbol</th>
                  <th className="text-left py-4 px-4 font-semibold text-gray-700 dark:text-white/70">Name</th>
                  <th className="text-left py-4 px-4 font-semibold text-gray-700 dark:text-white/70">Price</th>
                  <th className="text-left py-4 px-4 font-semibold text-gray-700 dark:text-white/70">Change</th>
                  <th className="text-left py-4 px-4 font-semibold text-gray-700 dark:text-white/70">Spread</th>
                  <th className="text-left py-4 px-4 font-semibold text-gray-700 dark:text-white/70">Volume</th>
                  <th className="text-left py-4 px-4 font-semibold text-gray-700 dark:text-white/70">Action</th>
                </tr>
              </thead>
              <tbody>
                {filteredSymbols.map((symbol, index) => (
                  <tr
                    key={index}
                    className="border-b border-gray-100 dark:border-white/5 hover:bg-gray-50/50 dark:hover:bg-white/5 transition-colors duration-200"
                  >
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-1">
                          <span className="text-lg">{symbol.flag1}</span>
                          <span className="text-lg">{symbol.flag2}</span>
                        </div>
                        <div className="font-bold text-gray-900 dark:text-white">{symbol.symbol}</div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="text-sm text-gray-700 dark:text-white/70">{symbol.name}</div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="font-mono text-lg font-bold text-gray-900 dark:text-white">{symbol.price}</div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        {symbol.trend === "up" ? (
                          <TrendingUp className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                        ) : (
                          <TrendingDown className="w-4 h-4 text-red-600 dark:text-red-400" />
                        )}
                        <div
                          className={`font-bold ${
                            symbol.trend === "up"
                              ? "text-blue-600 dark:text-blue-400"
                              : "text-red-600 dark:text-red-400"
                          }`}
                        >
                          {symbol.change} ({symbol.changePercent})
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="text-sm text-gray-700 dark:text-white/70">{symbol.spread} pips</div>
                    </td>
                    <td className="py-4 px-4">
                      <Badge className={getVolumeColor(symbol.volume)}>{symbol.volume}</Badge>
                    </td>
                    <td className="py-4 px-4">
                      <Button
                        size="sm"
                        className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600"
                      >
                        Trade
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Trading Features */}
        <div className="mt-12">
          <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-8 shadow-xl relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10" />
            <div className="relative z-10">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Why Trade With Us?</h3>
                <p className="text-lg text-gray-700 dark:text-white/70">
                  Experience superior trading conditions across all major currency pairs
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">0.8</div>
                  <div className="text-sm text-gray-600 dark:text-white/60">Average Spread (pips)</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">{"<"}10ms</div>
                  <div className="text-sm text-gray-600 dark:text-white/60">Execution Speed</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">50+</div>
                  <div className="text-sm text-gray-600 dark:text-white/60">Currency Pairs</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">24/5</div>
                  <div className="text-sm text-gray-600 dark:text-white/60">Market Hours</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
