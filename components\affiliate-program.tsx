"use client"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Users, DollarSign, TrendingUp, Share2, Gift, Target, BarChart3, Zap, Crown, Star } from "lucide-react"

export default function AffiliateProgram() {
  const commissionTiers = [
    {
      tier: "Bronze",
      referrals: "1-10",
      commission: "15%",
      color: "orange",
      icon: Target,
      perks: ["Basic dashboard", "Monthly payouts", "Email support"],
    },
    {
      tier: "Silver",
      referrals: "11-25",
      commission: "20%",
      color: "gray",
      icon: Star,
      perks: ["Advanced analytics", "Bi-weekly payouts", "Priority support", "Marketing materials"],
    },
    {
      tier: "Gold",
      referrals: "26-50",
      commission: "25%",
      color: "yellow",
      icon: Crown,
      perks: [
        "Real-time tracking",
        "Weekly payouts",
        "Dedicated manager",
        "Custom landing pages",
        "Exclusive webinars",
      ],
      popular: true,
    },
    {
      tier: "Platinum",
      referrals: "51+",
      commission: "30%",
      color: "purple",
      icon: Zap,
      perks: [
        "Premium dashboard",
        "Daily payouts",
        "VIP support",
        "Co-marketing opportunities",
        "Revenue sharing",
        "Personal branding",
      ],
    },
  ]

  const stats = [
    {
      value: "$2.5M+",
      label: "Paid to Affiliates",
      icon: DollarSign,
      color: "blue",
    },
    {
      value: "5,000+",
      label: "Active Affiliates",
      icon: Users,
      color: "blue",
    },
    {
      value: "30%",
      label: "Max Commission",
      icon: TrendingUp,
      color: "purple",
    },
    {
      value: "24/7",
      label: "Support Available",
      icon: BarChart3,
      color: "orange",
    },
  ]

  const getColorClasses = (color: string, popular = false) => {
    const colorMap = {
      orange: {
        bg: "from-orange-500/10 to-amber-500/10 dark:from-orange-500/20 dark:to-amber-500/20",
        border: popular
          ? "border-orange-400/50 dark:border-orange-400/40"
          : "border-orange-200 dark:border-orange-400/20",
        icon: "text-orange-600 dark:text-orange-400",
        iconBg: "from-orange-500 to-amber-500",
      },
      gray: {
        bg: "from-gray-500/10 to-slate-500/10 dark:from-gray-500/20 dark:to-slate-500/20",
        border: popular ? "border-gray-400/50 dark:border-gray-400/40" : "border-gray-200 dark:border-gray-400/20",
        icon: "text-gray-600 dark:text-gray-400",
        iconBg: "from-gray-500 to-slate-500",
      },
      yellow: {
        bg: "from-yellow-500/10 to-amber-500/10 dark:from-yellow-500/20 dark:to-amber-500/20",
        border: popular
          ? "border-yellow-400/50 dark:border-yellow-400/40"
          : "border-yellow-200 dark:border-yellow-400/20",
        icon: "text-yellow-600 dark:text-yellow-400",
        iconBg: "from-yellow-500 to-amber-500",
      },
      purple: {
        bg: "from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20",
        border: popular
          ? "border-purple-400/50 dark:border-purple-400/40"
          : "border-purple-200 dark:border-purple-400/20",
        icon: "text-purple-600 dark:text-purple-400",
        iconBg: "from-purple-500 to-pink-500",
      },
      blue: {
        bg: "from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20",
        border: popular ? "border-blue-400/50 dark:border-blue-400/40" : "border-blue-200 dark:border-blue-400/20",
        icon: "text-blue-600 dark:text-blue-400",
        iconBg: "from-blue-500 to-cyan-500",
      },
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.orange
  }

  return (
    <section className="py-24 md:py-32 relative" aria-labelledby="affiliate-heading">
      <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
        {/* Section Header */}
        <div className="mb-20 md:mb-28 max-w-4xl mx-auto text-center">
          <Badge
            variant="outline"
            className="mb-8 md:mb-12 text-sm font-light border-gray-300 dark:border-white/20 text-gray-600 dark:text-white/80 px-4 py-2 items-center"
          >
            <Share2 className="w-4 h-4 mr-2" />
            Affiliate Program
          </Badge>
          <h2 id="affiliate-heading" className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 md:mb-12 leading-tight">
            Earn up to{" "}
            <span className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400 bg-clip-text text-transparent">
              30% commission
            </span>
          </h2>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 leading-relaxed">
            Join our affiliate program and earn generous commissions by referring traders to Apex Capital. The more you
            refer, the more you earn.
          </p>
        </div>

        {/* Stats Section */}
        <div className="mb-20 md:mb-28">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon
              return (
                <div
                  key={index}
                  className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 md:p-8 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] text-center group"
                >
                  <div
                    className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${getColorClasses(stat.color).iconBg} flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg`}
                  >
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <div className={`text-3xl md:text-4xl font-bold mb-2 ${getColorClasses(stat.color).icon}`}>
                    {stat.value}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-white/60">{stat.label}</div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Commission Tiers */}
        <div className="mb-20 md:mb-28">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">Commission Tiers</h3>
            <p className="text-lg text-gray-700 dark:text-white/70">
              Higher referral volumes unlock better commission rates and exclusive perks
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
            {commissionTiers.map((tier, index) => {
              const colors = getColorClasses(tier.color, tier.popular)
              const IconComponent = tier.icon

              return (
                <div key={index} className="relative group">
                  {tier.popular && (
                    <div className="absolute -top-4 left-1/2 -translate-x-1/2 z-20">
                      <Badge className="bg-blue-600 dark:bg-blue-500 text-white text-xs px-3 py-1 shadow-lg">
                        <Star className="w-3 h-3 mr-1" />
                        MOST POPULAR
                      </Badge>
                    </div>
                  )}

                  <div
                    className={`bg-gradient-to-br ${colors.bg} backdrop-blur-sm border-2 ${colors.border} rounded-3xl p-6 md:p-8 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] relative overflow-hidden h-full ${tier.popular ? "ring-2 ring-blue-400/20 dark:ring-blue-400/30" : ""}`}
                  >
                    <div
                      className={`absolute inset-0 bg-gradient-to-br ${colors.bg} opacity-0 group-hover:opacity-100 transition-opacity duration-500`}
                    />

                    <div className="relative z-10 h-full flex flex-col">
                      {/* Tier Header */}
                      <div className="text-center mb-6">
                        <div
                          className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${colors.iconBg} flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:scale-110 transition-transform duration-300`}
                        >
                          <IconComponent className="w-8 h-8 text-white" />
                        </div>
                        <h4 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-2">
                          {tier.tier}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-white/60 mb-4">{tier.referrals} referrals</p>
                        <div className={`text-4xl md:text-5xl font-bold ${colors.icon} mb-2`}>{tier.commission}</div>
                        <p className="text-sm text-gray-600 dark:text-white/60">Commission Rate</p>
                      </div>

                      {/* Perks */}
                      <div className="flex-grow">
                        <h5 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Perks Include:</h5>
                        <ul className="space-y-3">
                          {tier.perks.map((perk, perkIndex) => (
                            <li key={perkIndex} className="flex items-start gap-3">
                              <div
                                className={`w-2 h-2 rounded-full ${colors.iconBg.replace("from-", "bg-").replace(" to-amber-500", "").replace(" to-slate-500", "").replace(" to-pink-500", "").replace(" to-cyan-500", "")} mt-2 flex-shrink-0`}
                              />
                              <span className="text-sm text-gray-700 dark:text-white/70">{perk}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* How It Works */}
        <div className="mb-20 md:mb-28">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">How It Works</h3>
            <p className="text-lg text-gray-700 dark:text-white/70">Start earning commissions in three simple steps</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
            <div className="text-center group">
              <div className="w-20 h-20 rounded-3xl bg-gradient-to-r from-blue-500/20 to-cyan-500/20 dark:from-blue-500/30 dark:to-cyan-500/30 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center text-white font-bold text-xl shadow-lg">
                  1
                </div>
              </div>
              <h4 className="text-xl md:text-2xl font-bold text-gray-900 dark:text-white mb-4">Sign Up</h4>
              <p className="text-gray-700 dark:text-white/70">
                Register for our affiliate program and get your unique referral link instantly
              </p>
            </div>

            <div className="text-center group">
              <div className="w-20 h-20 rounded-3xl bg-gradient-to-r from-blue-500/20 to-cyan-500/20 dark:from-blue-500/30 dark:to-cyan-500/30 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center text-white font-bold text-xl shadow-lg">
                  2
                </div>
              </div>
              <h4 className="text-xl md:text-2xl font-bold text-gray-900 dark:text-white mb-4">Share & Promote</h4>
              <p className="text-gray-700 dark:text-white/70">
                Share your link on social media, blogs, or directly with potential traders
              </p>
            </div>

            <div className="text-center group">
              <div className="w-20 h-20 rounded-3xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 dark:from-purple-500/30 dark:to-pink-500/30 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold text-xl shadow-lg">
                  3
                </div>
              </div>
              <h4 className="text-xl md:text-2xl font-bold text-gray-900 dark:text-white mb-4">Earn Commissions</h4>
              <p className="text-gray-700 dark:text-white/70">
                Receive commissions for every successful referral that purchases a challenge
              </p>
            </div>
          </div>
        </div>

        {/* Marketing Materials */}
        <div className="mb-20 md:mb-28">
          <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 md:p-12 shadow-xl relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10" />
            <div className="relative z-10">
              <div className="text-center mb-12">
                <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <Gift className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                  Marketing Materials Provided
                </h3>
                <p className="text-lg text-gray-700 dark:text-white/70">
                  We provide everything you need to succeed as an affiliate
                </p>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-500/20 to-cyan-500/20 dark:from-blue-500/30 dark:to-cyan-500/30 flex items-center justify-center mx-auto mb-3">
                    <BarChart3 className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h4 className="text-sm font-bold text-gray-900 dark:text-white mb-1">Banners & Ads</h4>
                  <p className="text-xs text-gray-600 dark:text-white/60">High-converting display ads</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-500/20 to-cyan-500/20 dark:from-blue-500/30 dark:to-cyan-500/30 flex items-center justify-center mx-auto mb-3">
                    <Share2 className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h4 className="text-sm font-bold text-gray-900 dark:text-white mb-1">Social Media</h4>
                  <p className="text-xs text-gray-600 dark:text-white/60">Ready-to-post content</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 dark:from-purple-500/30 dark:to-pink-500/30 flex items-center justify-center mx-auto mb-3">
                    <Target className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <h4 className="text-sm font-bold text-gray-900 dark:text-white mb-1">Landing Pages</h4>
                  <p className="text-xs text-gray-600 dark:text-white/60">Optimized conversion pages</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-yellow-500/20 to-orange-500/20 dark:from-yellow-500/30 dark:to-orange-500/30 flex items-center justify-center mx-auto mb-3">
                    <Users className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
                  </div>
                  <h4 className="text-sm font-bold text-gray-900 dark:text-white mb-1">Email Templates</h4>
                  <p className="text-xs text-gray-600 dark:text-white/60">Professional email campaigns</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-8 md:p-12 shadow-xl relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10" />
            <div className="relative z-10">
              <h3 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                Ready to Start Earning?
              </h3>
              <p className="text-lg text-gray-700 dark:text-white/70 mb-8 max-w-2xl mx-auto">
                Join thousands of successful affiliates earning substantial commissions with Apex Capital. Start your
                affiliate journey today!
              </p>
              <Button className="rounded-full bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600 px-8 py-4 text-lg hover:scale-105 transition-all duration-300 hover:shadow-lg">
                Join Affiliate Program
                <Share2 className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
